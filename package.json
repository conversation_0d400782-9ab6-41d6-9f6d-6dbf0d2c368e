{"name": "live-lobby", "private": true, "workspaces": ["packages/*", "site", "serve"], "scripts": {"build": "npm run build --workspaces --if-present", "publish": "npm run publish --workspaces --if-present", "clean": "npm run clean --workspaces --if-present && rm -rf node_modules package-lock.json", "lint": "npm run lint --workspaces --if-present", "lint:fix": "npm run lint:fix --workspaces --if-present", "site": "npm run dev --w site", "serve": "npm run dev --w serve", "site:stage": "npm run dev:stage --w site", "site:preview": "npm run preview --w site"}, "engines": {"node": ">=18.13"}}