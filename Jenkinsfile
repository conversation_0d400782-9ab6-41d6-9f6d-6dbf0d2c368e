#!/usr/bin/env groovy
pipeline {
  agent any
  options {
    disableConcurrentBuilds()
    timestamps()
    timeout(time: 1, unit: 'HOURS')
  }

  environment {
    NODE = "22"
  }

  stages {
    stage ('NPM install') {
      when {
        anyOf {
          expression { env.BRANCH_NAME.startsWith('PR-') }
          branch 'develop'
          branch 'stage'
          branch 'prod'
        }
      }
      steps {
        withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-jfrog-npmjs', targetLocation: '.npmrc')]) {
            sh 'npm install'
          }
        }
      }
    }

    stage('Check') {
      when {
        expression { env.BRANCH_NAME.startsWith('PR-') }
      }
      steps {
        withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-npmjs', targetLocation: '.npmrc')]) {
            sh 'npm run build'
            sh 'npm run lint'
          }
        }
      }
    }

    stage('Upload to QA') {
      when {
        branch 'develop'
      }
      steps {
        withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-jfrog-npmjs', targetLocation: '.npmrc')]) {
            script {
              env.VITE_PLAYER_SOCKET_API="https://player-api.qa-server.dev-qa.ss211208.com"
              sh 'npm run build'
            }
          }
        }
        withDockerContainer(image: "amazon/aws-cli", toolName: 'latest', args:'--entrypoint ""') {
          withCredentials([[
            $class: 'AmazonWebServicesCredentialsBinding',
            accessKeyVariable: 'AWS_ACCESS_KEY_ID',
            secretKeyVariable: 'AWS_SECRET_ACCESS_KEY',
            credentialsId: 'lobby-poc-lobby-ng'
          ]]) {
            sh 'aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID'
            sh 'aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY'
            sh "aws s3 cp ${WORKSPACE}/site/dist/ s3://lobby-poc-lobby-ng/ --recursive"
            sh "aws cloudfront create-invalidation --distribution-id EDQEZR17GZ7DO --paths '/*'"
          }
        }
      }
    }

    stage('Upload to STAGE') {
      when {
        branch 'stage'
      }
      steps {
        withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-jfrog-npmjs', targetLocation: '.npmrc')]) {
            script {
              env.VITE_PLAYER_SOCKET_API="https://player.gcpstg.m27613.com"
              sh 'npm run build'
            }
          }
        }
        withDockerContainer(image: "amazon/aws-cli", toolName: 'latest', args:'--entrypoint ""') {
          withCredentials([[
            $class: 'AmazonWebServicesCredentialsBinding',
            accessKeyVariable: 'AWS_ACCESS_KEY_ID',
            secretKeyVariable: 'AWS_SECRET_ACCESS_KEY',
            credentialsId: 'lobby-poc-lobby-ng'
          ]]) {
            sh 'aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID'
            sh 'aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY'
            sh "aws s3 cp ${WORKSPACE}/site/dist/ s3://lobby-stg-poc-lobby-ng/ --recursive"
            sh "aws cloudfront create-invalidation --distribution-id E2BTII7XTMLTCR --paths '/*'"
          }
        }
      }
    }

    stage('Upload to PROD') {
      when {
        branch 'prod'
      }
      steps {
        withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-jfrog-npmjs', targetLocation: '.npmrc')]) {
            script {
              env.VITE_PLAYER_SOCKET_API="https://api-player.hep200512.com"
              sh 'npm run build'
            }
          }
        }
        withDockerContainer(image: "amazon/aws-cli", toolName: 'latest', args:'--entrypoint ""') {
          withCredentials([[
            $class: 'AmazonWebServicesCredentialsBinding',
            accessKeyVariable: 'AWS_ACCESS_KEY_ID',
            secretKeyVariable: 'AWS_SECRET_ACCESS_KEY',
            credentialsId: 'lobby-softgate-live'
          ]]) {
            sh 'aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID'
            sh 'aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY'
            sh "aws s3 cp ${WORKSPACE}/site/dist/ s3://lobby-softgate-live/ --recursive"
            sh "aws cloudfront create-invalidation --distribution-id EZS5S1H1RWRT6 --paths '/*'"
            sh "aws cloudfront create-invalidation --distribution-id E1TVXBTOTGAJ6A --paths '/*'"
          }
        }
      }
    }

  }
}
