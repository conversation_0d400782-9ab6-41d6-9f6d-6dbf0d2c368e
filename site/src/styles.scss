@use './styles/reset';
@use './styles/fonts';
@use './styles/variables';

* {
  box-sizing: border-box;
  outline: none;
  -webkit-tap-highlight-color: transparent;
}

html {
  font-family: Lato, Arial, sans-serif;
  font-weight: 400;
  background: #000;
}

body {
  height: 100vh;
  min-height: 100vh;
  margin: 0;
  color: #fff;
  background: #000;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  @supports (-webkit-touch-callout: none) {
    /* CSS specific to iOS devices */
    height: 102vh;
  }
}

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  min-width: 120px;
  border: none;
  box-shadow: none;
  outline: none;
  background: variables.$color-default;
  padding: 0 20px;
  border-radius: 3px;
  font-size: 14px;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  color: #000;

  .desktop & {
    &:hover {
      background: #fff !important;
    }

    &--highlight {
      background: variables.$color-highlight;

      &:hover {
        background: #fff !important;
      }
    }
  }
}
