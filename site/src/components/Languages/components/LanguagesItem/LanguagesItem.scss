@use "../../../../styles/variables";

.languages-item {
  $this: &;
  display: flex;
  align-items: center;
  height: 40px;
  cursor: pointer;
  font-size: 14px;
  &_active {
    #{$this}__id,
    #{$this}__value {
      color: variables.$color-highlight;
    }
  }
  &:hover {
    #{$this}__value {
      color: #fff;
    }
  }
  &__id {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    height: 100%;
    width: 72px;
    text-transform: uppercase;
    color: #fff;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    white-space: nowrap;
    transition: color 0.15s ease-in-out;
  }
  &__value {
    display: flex;
    align-items: center;
    flex-grow: 1;
    height: 100%;
    padding: 0 10px;
    transition: color 0.15s ease-in-out;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-transform: capitalize;
    overflow: hidden;
    color: #989898;
  }
}
