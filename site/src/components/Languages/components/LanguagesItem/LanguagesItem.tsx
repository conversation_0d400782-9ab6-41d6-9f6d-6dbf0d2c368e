import type { MouseEvent } from 'react'
import cn from 'classnames'
import './LanguagesItem.scss'
import { useTranslate } from '@/hooks/useTranslate.ts'

interface Props {
  id: string
  onClick: (id: string) => void
  isActive?: boolean
}

export const LanguagesItem = ({ id, isActive, onClick }: Props) => {
  const { t } = useTranslate()

  const handleClick = (e: MouseEvent) => {
    e.stopPropagation()
    onClick(id)
  }
  return (
    <div className={cn('languages-item', { 'languages-item_active': isActive })} onClick={handleClick}>
      <div className="languages-item__id">{id}</div>
      <div className="languages-item__value">{t(`LANGUAGES.${id}`)}</div>
    </div>
  )
}
