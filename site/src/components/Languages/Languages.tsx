import { LanguagesItem } from './components/LanguagesItem/LanguagesItem'
import './Languages.scss'
import { LANGUAGE_CODES } from '@/services/translations.ts'

export const LANGUAGES = Array.from(new Set(Object.values(LANGUAGE_CODES)))

interface Props {
  activeItemId: string
  onSelect: (id: string) => void
}

export const Languages = ({ activeItemId, onSelect }: Props) => (
  <div className="languages-list">
    {LANGUAGES.map((lang) => (
      <LanguagesItem key={lang} id={lang} isActive={lang === activeItemId} onClick={onSelect} />
    ))}
  </div>
)
