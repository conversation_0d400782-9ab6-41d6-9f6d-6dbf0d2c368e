import type { MenuItem } from '@/models/lobby.ts'
import { FullPage } from '../FullPage/FullPage.tsx'
import './MenuPage.scss'
import { Section } from '@/components/Section/Section.tsx'
import { useLiveManagerSocket } from '@/hooks/useLiveManagerSocket'
import { useEffect } from 'react'

interface Props {
  menuItem: MenuItem
}

export const MenuPage = ({ menuItem }: Props) => {
  const { subscribe } = useLiveManagerSocket()

  useEffect(() => subscribe(menuItem.tableIds), [menuItem, subscribe])

  if (menuItem.fullPage) {
    return <FullPage {...menuItem.fullPage} />
  }
  if (!menuItem.sections) {
    return null
  }
  return (
    <main className="main">
      {menuItem.sections.map(section => (
        <Section key={section.id} section={section} isGridLayout={menuItem.isGridLayout} />
      ))}
    </main>
  )
}
