import './NicknameModal.scss'
import { Modal } from '@/components/Modal/Modal.tsx'
import { useStore } from '@/hooks/useStore.ts'
import { NicknameForm } from '@/components/NicknameModal/components/NicknameForm/NicknameForm.tsx'

export const NicknameModal = () => {
  const isNicknameModalOpen = useStore(state => state.isNicknameModalOpen)
  const setIsNicknameModalOpen = useStore(state => state.setIsNicknameModalOpen)
  
  const close = () => {
    setIsNicknameModalOpen(false)
  }
  
  return (
    <Modal isOpen={isNicknameModalOpen} onClose={close}>
      <NicknameForm onClose={close} />
    </Modal>
  )
}
