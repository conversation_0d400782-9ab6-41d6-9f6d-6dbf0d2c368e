import './NicknameForm.scss'
import type { ChangeEvent, FormEvent } from 'react'
import { useEffect, useRef, useState } from 'react'
import { Icon } from '@/components/Icon/Icon.tsx'
import { IconType } from '@/components/Icon/types.ts'
import type { ValidationErrors } from '@/models/validators.ts'
import { nicknameServerValidationErrors, Validators } from '@/models/validators.ts'
import { useStore } from '@/hooks/useStore.ts'
import { useTranslate } from '@/hooks/useTranslate.ts'
import type { ErrorData } from '@/models/error.ts'

interface FormData {
  nickname: string;
}

interface Props {
  onClose: () => void
}

export const NicknameForm = ({ onClose }: Props) => {
  const { t } = useTranslate()
  const playerInfo = useStore((state) => state.playerInfo)
  const changeNickname = useStore(state => state.changeNickname)

  const [formData, setFormData] = useState<FormData>({ nickname: playerInfo?.nickname ?? '' })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isInfoVisible, setIsInfoVisible] = useState(false)
  const [isValid, setIsValid] = useState(false)
  const [error, setError] = useState<ValidationErrors | null>(null)

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  const validateValue = (value: string) => {
    const validators = [
      Validators.NicknamePattern(),
      Validators.MinLength(2),
      Validators.MaxLength(15),
      Validators.NotLogin(playerInfo?.code)
    ]

    for (const validator of validators) {
      const error = validator(value)
      if (error) {
        return error
      }
    }
    return null
  }

  const handleTextChange = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value
    const error = validateValue(value)
    setError(error)
    setIsValid(error === null)
    setFormData({ nickname: value })
  }

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    if (error) {
      return
    }
    setIsSubmitting(true)
    changeNickname(formData.nickname)
      .then(playerInfo => {
        if (playerInfo) {
          onClose()
        }
      })
      .catch((error: ErrorData) => {
        const { status, code } = error
        const errors = status === 400 ? nicknameServerValidationErrors(code) : null
        setError(errors)
      })
      .finally(() => {
        setIsSubmitting(false)
      })
  }

  return (
    <form className="nickname-form" onSubmit={handleSubmit}>
      <div
        className={`nickname-form__question ${isInfoVisible ? 'nickname-form__question_active' : ''}`}
        onClick={() => setIsInfoVisible(!isInfoVisible)}
      >
        <Icon type={IconType.Question} />
      </div>

      <div
        className="nickname-form__close"
        onClick={onClose}
      >
        <Icon type={IconType.Close} />
      </div>

      <div className="nickname-form__title">
        {t('PLAYER_INFO.NICKNAME.enterNick')}
        <br />
        {t('PLAYER_INFO.NICKNAME.notRealName')}
      </div>
      <label className="nickname-form__label">
        <input
          ref={inputRef}
          className="nickname-form__input"
          type="text"
          value={formData.nickname}
          onChange={handleTextChange}
          maxLength={15}
        />
        <span className="nickname-form__counter">
          {formData.nickname.length}/15
        </span>
      </label>
      {error && (
        <div className="nickname-form__error">
          {error && Object.keys(error).map((key) => (
            <div key={key}>{t(`SERVER_ERRORS.NICKNAME.${key}`)}</div>
          ))}
        </div>
      )}
      <div
        className={`nickname-form__info ${isInfoVisible ? 'nickname-form__info_visible' : ''}`}
        dangerouslySetInnerHTML={{ __html: t('PLAYER_INFO.NICKNAME.instructions') }}
      >
      </div>
      <div className="nickname-form__controls">
        <button
          className="nickname-form__button"
          type="submit"
          disabled={isSubmitting || !isValid}
        >
          {t('ACTIONS.save')}
        </button>
        <button
          className="nickname-form__button nickname-form__button_highlight" type="button"
          onClick={onClose}
        >
          {t('LOGOUT.cancelButtonTitle')}
        </button>
      </div>
    </form>
  )
}
