@use '../../../../styles/variables';

.nickname-form {
  position: relative;
  height: 100%;
  width: 100%;
  max-width: 340px;
  padding: 2em 1.5em;
  font-size: 16px;

  &__input {
    display: block;
    height: 2em;
    width: 100%;
    padding: 0 2em 0 0;
    font-size: .85em;
    color: #fff;
    border: none;
    box-shadow: none;
    background-color: transparent;
    border-bottom: 1px solid #363636;
    outline: none;
  }

  &__button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2.4em;
    min-width: 9em;
    border: none;
    box-shadow: none;
    outline: none;
    background: #707070;
    padding: 0 1em;
    border-radius: 0.3em;
    text-transform: uppercase;
    transition: all .3s ease-in-out;
    color: #000;
    margin-right: 1.5em;
    &:not(:disabled) {
      cursor: pointer;
    }

    &:last-child {
      margin-right: 0;
    }

    &_highlight {
      background-color: variables.$color-highlight;
    }

    &:hover {
      &:not(:disabled) {
        background: #fff;
      }
    }
  }

  &__counter {
    position: absolute;
    font-size: .85em;
    top: 0.55em;
    right: 0;
    line-height: 1;
    color: #606060;
  }

  &__label {
    position: relative;
  }

  &__controls {
    display: flex;
    justify-content: center;
    width: 100%;
    padding-top: 2em;
  }

  &__title {
    margin-bottom: 1em;
    line-height: 1.2;
    font-size: .85em;
    font-weight: 600;
    text-align: center;
  }

  &__info {
    display: none;
    font-size: .85em;
    line-height: 1.2;
    color: #606060;
    text-align: center;
    margin-top: 0.5em;

    &_visible {
      display: block;
    }
  }

  &__question {
    position: absolute;
    top: 0.5em;
    right: 2.5em;
    width: 1.125em;
    height: 1.125em;
    cursor: pointer;
    transition: fill .3s ease-in-out;

    svg {
      width: 100%;
      height: 100%;
      fill: #606060;
    }

    &_active {
      svg {
        fill: variables.$color-highlight;
      }
    }

    &:hover {
      svg {
        fill: #fff;
      }
    }
  }

  &__close {
    position: absolute;
    top: 0.5em;
    right: 0.5em;
    width: 1.125em;
    height: 1.125em;
    cursor: pointer;
    overflow: hidden;

    svg {
      width: 100%;
      height: 100%;
      fill: #606060;
      transition: fill .3s ease-in-out;
    }

    &:hover {
      svg {
        fill: #fff;
      }
    }
  }

  &__error {
    font-size: .85em;
    line-height: 1.2;
    color: #c14141;
    text-align: center;
    min-height: 2em;
    margin-top: 0.3em;
  }
}
