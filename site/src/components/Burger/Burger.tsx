import './Burger.scss'
import type { MouseEvent } from 'react'
import { useStore } from '@/hooks/useStore.ts'

interface Props {
  className?: string
}

export const Burger = ({ className }: Props) => {
  const setOpenAsideMenu = useStore(state => state.setOpenAsideMenu)
  const handleClick = (e: MouseEvent) => {
    e.stopPropagation()
    setOpenAsideMenu(true)
  }

  return (
    <button className={`burger ${className ?? ''}`} onClick={handleClick}>
      <span className="burger__line"></span>
      <span className="burger__line"></span>
      <span className="burger__line"></span>
    </button>
  )
}
