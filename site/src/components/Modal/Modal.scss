@use "../../styles/variables";

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, .5);
  z-index: 901;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  &__dialog {
    position: relative;
    z-index: 1;
    background: rgba(0, 0, 0, 0.9);
    box-shadow: none;
    border-radius: 5px;
    max-width: 100%;
    max-height: 100%;
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: auto;

    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background: #282a35;
    }

    &::-webkit-scrollbar {
      width: 8px;
      background: #282a35;
    }

    &::-webkit-scrollbar-thumb {
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 3px;
      background-color: #000;
    }

    &::-webkit-scrollbar:horizontal {
      display: none;
    }
  }

  &__close {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2em;
    height: 2em;
    cursor: pointer;
    transition: color 150ms ease-in-out;

    &:hover {
      fill: variables.$color-highlight;
    }
  }

  &__icon {
    fill: #a7a8ab;
    width: 1em;
    height: 1em;
  }

  @media (orientation: portrait) and (max-width: 500px) {
    font-size: 4.27vw;
  }

  @media (orientation: landscape) and (max-height: 500px) and (max-width: 1024px) {
    font-size: 4.27vh;
  }
}
