import type { PropsWithChildren, MouseEvent } from 'react'
import './Modal.scss'
import { Icon } from '../Icon/Icon.tsx'
import { IconType } from '../Icon/types.ts'

interface Props {
  isOpen: boolean;
  onClose: () => void;
  showCloseButton?: boolean
}

export const Modal = ({ isOpen, onClose, showCloseButton, children }: PropsWithChildren<Props>) => {
  if (!isOpen) return null
  
  const handleClose = (e: MouseEvent) => {
    e.stopPropagation()
    
    onClose()
  }

  return (
    <div className="modal" onClick={e => handleClose(e)}>
      <div className="modal__dialog" onClick={(e) => e.stopPropagation()}>
        {showCloseButton && <div className="modal__close" onClick={e => handleClose(e)}>
          <Icon className="modal__icon" type={IconType.Close} />
        </div>}
        {children}
      </div>
    </div>
  )
}
