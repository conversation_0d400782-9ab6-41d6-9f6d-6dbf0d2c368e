import { createContext, useState, useEffect } from 'react'

interface DeviceContextProps {
  isMobile: boolean
  isPortrait: boolean
  isMac: boolean
}

const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Kindle|Silk|Windows Phone/i
const isMobile = mobileRegex.test(navigator.userAgent)
const isMac = navigator.userAgent.includes('Mac');

const DeviceContext = createContext<DeviceContextProps>({ isMobile: false, isPortrait: false, isMac: false })

interface DeviceProviderProps {
  children: React.ReactNode
}

const DeviceProvider= ({ children }: DeviceProviderProps) => {
  const [isPortrait, setIsPortrait] = useState(false)
  
  useEffect(() => {
    document.body.classList.add(isMobile ? 'mobile' : 'desktop')
    
    if (isMobile) {
      // document.body.classList.add('layout-list')
    }
    
    const mediaQuery = window.matchMedia('(orientation: portrait)')
    setIsPortrait(mediaQuery.matches)
    
    const handleOrientationChange = () => {
      setIsPortrait(mediaQuery.matches)
    }
    mediaQuery.addEventListener('change', handleOrientationChange)
    return () => {
      mediaQuery.removeEventListener('change', handleOrientationChange)
    }
  }, [])
  
  return (
    <DeviceContext.Provider value={{ isMobile, isPortrait, isMac }}>
      {children}
    </DeviceContext.Provider>
  )
}

export { DeviceProvider, DeviceContext }
