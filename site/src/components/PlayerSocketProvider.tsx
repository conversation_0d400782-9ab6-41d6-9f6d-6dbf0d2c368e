import { type PropsWithChildren, useEffect, useRef } from 'react'
import { getAppConfig } from '../utils/appConfig.ts'
import { getPlayerCurrency, getPlayerToken, setPlayerCurrency } from '../utils/playerInfo.ts'
import { io, type Socket } from 'socket.io-client'
import { useStore } from '@/hooks/useStore.ts'
import { useErrorHandling } from '@/hooks/useErrorHandling.ts'
import { PlayerSocketContext } from '@/contexts/PlayerSocketContext.ts'
import type { FavoriteGameData, PlayerData, RecentlyGameData } from '@/models/playerInfo.ts'
import { useRouterStore } from '@/hooks/useRouterStore.ts'

export const PLAYER_INFO_INTERVAL = 5 * 1000

interface PlayerError {
  code?: number | string
  message?: string
  extraData: Record<string, unknown>
}

interface PlayerSocketEvents {
  'player-info': (data: PlayerData) => void;
  'game-favorite': (data: FavoriteGameData) => void;
  'game-played': (data: RecentlyGameData) => void;
  'player-error': (data: PlayerError) => void;
  'reconnect_attempt': (attemptNumber: number) => void
}

interface ClientEvents {
  'get-player-info': (data: { token: string }) => void;
}

type PlayerSocket = Socket<PlayerSocketEvents, ClientEvents>

export const PlayerSocketProvider = ({ children }: PropsWithChildren) => {
  const socketRef = useRef<PlayerSocket>(null)
  const refreshRef = useRef<number | undefined>(undefined)
  const liveManagerEndpoint = useStore(state => state.liveManagerEndpoint)
  const setPlayerInfo = useStore(state => state.setPlayerInfo)
  const setFavoriteGame = useStore(state => state.setFavoriteGame)
  const setRecentGame = useStore(state => state.setRecentGame)
  const { route } = useRouterStore()
  const { handleError } = useErrorHandling()

  useEffect(() => {
    const token = getPlayerToken()
    if (!socketRef.current && liveManagerEndpoint && token) {
      const socket: PlayerSocket = io(getAppConfig().playerSocketUrl, {
        transports: ['websocket'],
        path: `/socket-v4/socket.io`,
        query: {
          sw_ts: Date.now(),
          sw_player_token: token
        }
      })
      socket.on('reconnect_attempt', () => {
        socket.io.opts.transports = ['polling', 'websocket']
      })
      socket.on('connect', () => {
        console.info('[lobby] player socket connected')
      })
      socket.on('disconnect', () => {
        console.info('[lobby] player socket disconnected')
      })

      socket.on('player-info', ({ balances, ...payload }) => {
        const currency = payload.currency ?? getPlayerCurrency()
        setPlayerCurrency(currency)
        setPlayerInfo({
          ...payload,
          currency,
          balance: balances?.[currency].main
        })
      })
      socket.on('game-favorite', ({ gameCode, isFavorite }) => {
        setFavoriteGame(gameCode, isFavorite)
      })
      socket.on('game-played', ({ gameCode, time }) => {
        setRecentGame(gameCode, time)
      })
      socket.on('player-error', (data) => {
        if (data.code === 2013 || data.code === 'ECONNRESET') {
          console.error('[lobby] Player socket error', data)
        } else {
          const err = new Error()
          Object.assign(err, data)
          handleError(err)
        }
      })
      socket.emit('get-player-info', { token })

      socketRef.current = socket

      return () => {
        const socket = socketRef.current
        if (socket?.connected) {
          socket.close()
        }
      }
    }
  }, [handleError, liveManagerEndpoint, setFavoriteGame, setPlayerInfo, setRecentGame])

  useEffect(() => {
    const onVisibilityChange = () => {
      const socket = socketRef.current
      if (socket) {
        if (document.hidden) {
          console.info('[lobby] Pause player info refresh')
          if (refreshRef.current) {
            window.clearInterval(refreshRef.current)
            refreshRef.current = undefined
          }
          if (socket.connected) {
            socket.close()
          }
        } else {
          console.info('[lobby] Resume player info refresh')
          if (!socket.connected) {
            socket.open()
          }
          const token = getPlayerToken()
          if (route.name && token) {
            socket.emit('get-player-info', { token })
          }
          refreshRef.current = window.setInterval(() => {
            const token = getPlayerToken()
            if (route.name && token) {
              socket.emit('get-player-info', { token })
            }
          }, PLAYER_INFO_INTERVAL)
        }
      }
    }

    const token = getPlayerToken()
    if (route.name && token) {
      socketRef.current?.emit('get-player-info', { token })
    }
    refreshRef.current ??= window.setInterval(() => {
      const token = getPlayerToken()
      if (route.name && token) {
        socketRef.current?.emit('get-player-info', { token })
      }
    }, PLAYER_INFO_INTERVAL);

    document.addEventListener('visibilitychange', onVisibilityChange, false)
    return () => {
      document.removeEventListener('visibilitychange', onVisibilityChange)
      if (refreshRef.current) {
        window.clearInterval(refreshRef.current)
        refreshRef.current = undefined
      }
    }
  }, [route.name])

  return (
    <PlayerSocketContext.Provider value={null}>
      {children}
    </PlayerSocketContext.Provider>
  )
}
