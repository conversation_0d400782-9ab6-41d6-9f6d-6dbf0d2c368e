import { useEffect, useState, type PropsWithChildren } from 'react'
import { LobbyOptionsContext } from '@/contexts/LobbyOptionsContext'
import { getTerminalApi } from '@/services/terminalApi'
import type { LobbyInfoOptions } from '@/models/lobby'
import { useErrorHandling } from '@/hooks/useErrorHandling'
import { setLobbyId } from '@/utils/lobbyId.ts'

export const LobbyOptionsProvider = ({ children }: PropsWithChildren) => {
  const [loading, setLoading] = useState(true)
  const [lobbyOptions, setLobbyOptions] = useState<LobbyInfoOptions | null>(null)
  const { handleError } = useErrorHandling()

  useEffect(() => {
    setLoading(true)
    getTerminalApi().options()
      .then(options => {
        if (options) {
          setLobbyId(options.lobbyId ?? null)
          setLobbyOptions(options)
        } else {
          handleError(new Error('Failed to load lobby options'))
        }
      })
      .catch(handleError)
      .finally(() => {
        setLoading(false)
      })
  }, [handleError])

  if (loading) {
    return <div>Loading...</div>
  }
  return (
    <LobbyOptionsContext.Provider value={lobbyOptions}>
      {children}
    </LobbyOptionsContext.Provider>
  )
}
