import './Welcome.scss'
import { useTranslate } from '@/hooks/useTranslate.ts'
import { useStore } from '@/hooks/useStore.ts'

interface Props {
  className?: string
}

export const Welcome = ({ className }: Props) => {
  const { t } = useTranslate()
  const playerInfo = useStore(state => state.playerInfo)
  const setIsNicknameModalOpen = useStore(state => state.setIsNicknameModalOpen)

  if (!playerInfo?.nickname) {
    return null
  }
  
  const handleNicknameClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation()
    
    setIsNicknameModalOpen(true)
  }
  
  return (
    <div className={`welcome ${className ?? ''}`}>
      <div className="welcome__title">{t('HEADER.welcomeTitle')}</div>
      <div className="welcome__nickname" onClick={e => handleNicknameClick(e)}>
        {playerInfo?.nickname ?? ''}
      </div>
    </div>
  )
}
