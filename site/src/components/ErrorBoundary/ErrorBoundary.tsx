import type { PropsWithChildren } from 'react'
import { useState } from 'react'
import { ErrorContext } from '@/contexts/ErrorContext.ts'
import type { ErrorData } from '@/models/error.ts'
import { TokenIsMissing } from '@/models/error.ts'
import { useTranslate } from '@/hooks/useTranslate.ts'
import { useStore } from '@/hooks/useStore.ts'
import './ErrorBoundary.scss'
import { setPlayerToken } from '@/utils/playerInfo.ts'

export const ErrorBoundary = ({ children }: PropsWithChildren) => {
  const [error, setError] = useState<ErrorData | null>(null)
  const setPlayerInfo = useStore(state => state.setPlayerInfo)
  const { t } = useTranslate()

  function handleError(error: Error) {
    if (error instanceof TokenIsMissing) {
      error.message ||= t('EXPIRATION.message')
    }
    console.error(error)
    setPlayerToken(null)
    setPlayerInfo(null)
    setError(error)
  }

  if (error) {
    const { code, message, extraData: { traceId } = {} } = error
    return (
      <div className="error-page">
        <div className="error-page__dialog">
          {message && <div className="error-page__message">{message}</div>}
          {code && <div className="error-page__code">{t('SERVER_ERRORS.errorCode', { value: code.toString() })}</div>}
          {traceId && <div className="error-page__trace">{t('SERVER_ERRORS.traceId', { value: traceId })}</div>}
        </div>
      </div>
    )
  }

  return (
    <ErrorContext.Provider value={{ handleError }}>
      {children}
    </ErrorContext.Provider>
  )
}
