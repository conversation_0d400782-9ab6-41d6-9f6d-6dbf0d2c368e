import { useMemo } from 'react'
import { type FullPageGameType, SwFullPage } from 'sw-fullpage'
import 'sw-fullpage/dist/index.scss'
import { useErrorHandling } from '@/hooks/useErrorHandling.ts'
import { useTranslate } from '@/hooks/useTranslate.ts'
import { useRouterStore } from '@/hooks/useRouterStore.ts'
import { useDeviceStore } from '@/hooks/useDeviceStore.ts'
import { useStore } from '@/hooks/useStore.ts'
import { isGameOpen } from '@/utils/game.ts'
import type { GameLiveInfo } from '@/models/game.ts'
import { useRefreshDealerPicture } from '@/hooks/useRefreshDealerPicture.ts'
import { getMoneyFormatter } from '@/services/moneyFormatter.ts'

interface Props {
  game: GameLiveInfo
  gameType: FullPageGameType
}

export const FullPage = ({ game, gameType }: Props) => {
  const statuses = useStore((state) => state.statuses)
  const openActiveGame = useStore((state) => state.openActiveGame)
  const setLimitsActiveTable = useStore((state) => state.setLimitsActiveTable)
  const limit = useStore(state => state.limits.find(
    item => item.tableId === game.table?.tableId && item.gameCode === game.code
  )?.name)
  
  const thumbnailUrl = useStore((state) => {
    const url = game.table?.tableId ? state.dealerPictures.get(game.table.tableId) : undefined
    return url ?? game.table?.dealer?.picture
  })
  
  const { navigate } = useRouterStore()
  const { language } = useTranslate()
  const { handleError } = useErrorHandling()
  const { isMobile, isPortrait } = useDeviceStore()

  const isGameOnline = useMemo(() => isGameOpen(statuses)(game), [game, statuses])

  useRefreshDealerPicture(isGameOnline ? [game] : [])

  const selectedLimits = useMemo(() => {
    const limits = game.table?.limits
    if (limits) {
      return game.table?.limits.find(({ name }) => name === limit)
    }
  }, [game.table?.limits, limit])

  const selectedLimitString = useMemo(() => {
    if (selectedLimits) {
      return getMoneyFormatter().formatGameLimits(selectedLimits)
    }
  }, [selectedLimits])

  const isLimitEditable = (game?.table?.limits.length ?? 0) > 1

  const handlePlay = () => {
    openActiveGame(game.code, language, (replace) => {
      navigate(`play/${game.code}`, { replace })
    }).catch(handleError)
  }

  const handleLimitsEdit = () => {
    if (game.table) {
      setLimitsActiveTable(game.table)
    }
  }

  return (
    <SwFullPage
      dealerPicture={thumbnailUrl}
      language={language}
      gameType={gameType}
      isOnline={isGameOnline}
      isMobile={isMobile}
      isPortrait={isPortrait}
      onPlay={handlePlay}
      onLimitEdit={handleLimitsEdit}
      selectedLimitString={selectedLimitString ?? ''}
      isLimitEditable={isLimitEditable}
    />
  )
}
