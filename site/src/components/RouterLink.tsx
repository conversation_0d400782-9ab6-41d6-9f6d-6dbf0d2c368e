import type { MouseEvent, PropsWithChildren } from 'react'
import { useRouterStore } from '../hooks/useRouterStore.ts'

interface Props {
  to: string
  className?: string
}

export const RouterLink = ({ to, children, className }: PropsWithChildren<Props>) => {
  const { navigate } = useRouterStore()

  const handleClick = (e: MouseEvent) => {
    e.preventDefault()
    navigate(to)
  }

  return (
    <div className={className} onClick={handleClick}>
      {children}
    </div>
  )
}
