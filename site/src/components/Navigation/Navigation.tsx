import { type TouchEvent, useCallback, useEffect, useRef, useState } from 'react'
import { RouterLink } from '../RouterLink'
import { useTranslate } from '@/hooks/useTranslate.ts'
import { RibbonIcon } from '@/components/RibbonIcon/RibbonIcon.tsx'
import { useDeviceStore } from '@/hooks/useDeviceStore.ts'
import { NavigationIcon } from '@/components/Navigation/components/NaviagtionIcon/NavigationIcon.tsx'
import './Naviagation.scss'
import { useStore } from '@/hooks/useStore.ts'

interface Props {
  className?: string
}

export const Navigation = ({ className }: Props) => {
  const { translate } = useTranslate()
  const { isMobile, isMac } = useDeviceStore()
  const menuItem = useStore(state => state.menuItem)
  const menuItems = useStore(state => state.menuItems)
  const itemWidth = isMobile ? 88 : 120

  const containerRef = useRef<HTMLDivElement>(null)
  const trackRef = useRef<HTMLDivElement>(null)
  const [translateX, setTranslateX] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const [currentTranslate, setCurrentTranslate] = useState(0)
  const [maxTranslate, setMaxTranslate] = useState(0)

  const updateDimensions = useCallback(() => {
    if (containerRef.current && trackRef.current) {
      const containerWidth = containerRef.current.offsetWidth
      const trackWidth = trackRef.current.scrollWidth
      const newMaxTranslate = Math.max(0, trackWidth - containerWidth)
      setMaxTranslate(newMaxTranslate)
      setTranslateX(prev => Math.min(prev, newMaxTranslate))
    }
  }, [])

  useEffect(() => {
    const observer = new ResizeObserver(updateDimensions)
    if (trackRef.current) observer.observe(trackRef.current)
    return () => observer.disconnect()
  }, [updateDimensions])

  const translateXRef = useRef(translateX)
  const [isHovered, setIsHovered] = useState(false)

  useEffect(() => {
    translateXRef.current = translateX
  }, [translateX])

  const handleWheel = useCallback((e: WheelEvent) => {
    const isHorizontalScroll = Math.abs(e.deltaX) > Math.abs(e.deltaY)
    let delta = 0
    let canScroll = false

    if (isHorizontalScroll) {
      // Touchpad horizontal scroll
      delta = e.deltaX
      canScroll = true
    } else if (Math.abs(e.deltaY) > 0) {
      // Mouse wheel vertical scroll
      delta = e.deltaY
      const currentTranslate = translateXRef.current

      // Check scroll boundaries
      if (delta > 0) { // Scroll down = scroll right
        canScroll = currentTranslate < maxTranslate
      } else { // Scroll up = scroll left
        canScroll = currentTranslate > 0
      }
    }

    if (canScroll) {
      e.preventDefault()
      const sensitivity = isMac ? 1.5 : 2
      const newTranslate = translateXRef.current + delta * sensitivity
      const clamped = Math.max(0, Math.min(maxTranslate, newTranslate))

      setTranslateX(clamped)
    }
  }, [isMac, maxTranslate])

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const handler = (e: WheelEvent) => {
      if (isHovered) handleWheel(e)
    }

    container.addEventListener('wheel', handler, { passive: false })
    return () => container.removeEventListener('wheel', handler)
  }, [handleWheel, isHovered])

  // Dragging handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setStartX(e.clientX)
    setCurrentTranslate(translateX)
  }

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return

    const delta = e.clientX - startX
    const newTranslate = currentTranslate - delta
    setTranslateX(Math.max(0, Math.min(maxTranslate, newTranslate)))
  }, [currentTranslate, isDragging, maxTranslate, startX])

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  const handleTouchStart = (e: TouchEvent) => {
    setIsDragging(true)
    setStartX(e.touches[0].clientX)
    setCurrentTranslate(translateX)
  }

  const handleTouchMove = (e: TouchEvent) => {
    if (!isDragging) return

    const delta = e.touches[0].clientX - startX
    const newTranslate = currentTranslate - delta
    setTranslateX(Math.max(0, Math.min(maxTranslate, newTranslate)))
  }

  const handleNext = () => {
    setTranslateX(prev => Math.min(maxTranslate, prev + itemWidth))
  }

  const handlePrev = () => {
    setTranslateX(prev => Math.max(0, prev - itemWidth))
  }

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    } else {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [handleMouseMove, handleMouseUp, isDragging])

  return (
    <nav ref={containerRef} className={`nav ${className ?? ''} ${isDragging ? 'nav_dragging' : ''}`}>
      <svg className="nav-svg-filter" xmlns="http://www.w3.org/2000/svg" version="1.1">
        <defs>
          <filter id="menu-item-filter" colorInterpolationFilters="sRGB">
            <feColorMatrix values="1 0 0 0 0 0 0.8117647058823529 0 0 0 0 0 0 0 0 0 0 0 1 0"
                           type="matrix"></feColorMatrix>
          </filter>
        </defs>
      </svg>
      <div
        ref={trackRef}
        className="nav__track"
        style={{
          transform: `translate3d(-${translateX}px, 0, 0)`,
          transition: isDragging ? 'none' : 'transform 0.3s ease'
        }}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onTouchEnd={() => setIsDragging(false)}
      >
        {menuItems.map(item => (
          <RouterLink
            className={`nav__item ${item.slug === menuItem?.slug ? 'nav__item_active' : ''}`}
            key={item.slug}
            to={item.slug}
          >
            <NavigationIcon icon={item.icon} className="nav__icon" />
            <div className="nav__title">{translate(item.title)}</div>
            <RibbonIcon className="nav__ribbon" ribbon={item.ribbon} />
          </RouterLink>
        ))}
      </div>
      {translateX !== 0 && <button className="nav__control nav__control_left" onClick={handlePrev} />}
      {translateX < maxTranslate && <button className="nav__control nav__control_right" onClick={handleNext} />}
    </nav>
  )
}
