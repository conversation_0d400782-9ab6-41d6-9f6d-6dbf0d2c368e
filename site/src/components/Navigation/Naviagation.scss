@use '../../styles/variables';

.nav {
  $this: &;
  display: flex;
  justify-content: center;
  position: relative;
  height: 100%;
  width: 100%;
  margin-bottom: -2px;
  overflow: hidden;
  touch-action: none;

  &_dragging {
    cursor: grab;
  }

  &__track {
    display: flex;
    justify-content: flex-start;
    height: 100%;
    min-width: 130px;
    box-sizing: border-box;
    align-items: center;
    position: relative;
    will-change: transform;
    user-select: none;
    width: max-content;
    touch-action: pan-y;
    margin: auto;
  }

  &__control {
    position: absolute;
    top: 0;
    z-index: 1;
    width: 30px;
    height: 100%;
    cursor: pointer;
    border: none;
    outline: none;

    &_left {
      left: 0;
      background: linear-gradient(90deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);
    }
    &_right {
      right: 0;
      width: 60px;
      background: linear-gradient(270deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);
    }
  }

  &__title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 103px;
    height: 24px;
    margin: 6px 0;
    font-size: 13px;
    font-weight: 500;
    line-height: 1.02;
    text-align: center;
    white-space: normal;
    overflow: hidden;
    -webkit-user-drag: none;
    user-select: none;
    flex-shrink: 0;
  }

  &__icon {
    display: block;
    height: 36px;
    width: 100%;
    padding: 1px 0;
    -webkit-user-drag: none;
    user-select: none;
  }

  &__item {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    height: 100%;
    width: 120px;
    padding: 0 8px;
    font-size: 13px;
    color: #fff;
    text-align: center;
    text-transform: uppercase;
    text-decoration: none;
    border-bottom-width: 2px;
    border-bottom-color: transparent;
    border-bottom-style: solid;
    user-select: none;

    &:not(#{$this}_dragging &) {
      cursor: pointer;
    }

    &_active {
      color: variables.$color-highlight;
      border-bottom-color: variables.$color-highlight;
      #{$this}__icon {
        filter: url(#menu-item-filter);
      }
    }
  }

  &__ribbon {
    position: absolute;
    top: 7px;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 13px;
    border-radius: 2px;
  }

  .desktop & {
    &:hover {
      #{$this}__control {
        &_right {
          background: linear-gradient(270deg, rgba(variables.$color-highlight, 1) 0%, rgba(variables.$color-highlight, 0) 100%);
        }
      }
    }

    &__item {
      &:hover {
        color: variables.$color-highlight;
        #{$this}__icon {
          filter: url(#menu-item-filter);
        }
      }
    }
  }

  .mobile & {
    justify-content: flex-start;

    &__icon {
      height: 32px;
    }

    &__title {
      font-size: 10px;
      width: 88px;
      height: 22px;
      margin: 3px 0;
    }

    &__item {
      justify-content: flex-end;
      padding-top: 15px;
      width: 88px;
      margin-right: 0;

      &_active {
        background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, .1) 100%);
      }
    }
  }
}

.nav-svg-filter {
  position: absolute;
  opacity: 0;
  visibility: hidden;
  display: block;
  width: 0;
  height: 0;
  margin: 0;
  padding: 0;
}
