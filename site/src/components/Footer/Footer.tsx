import './Footer.scss'
import { Balance } from './components/Balance/Balance.tsx'
import { Cashier } from './components/Cashier/Cashier.tsx'
import { Home } from './components/Home/Home.tsx'
import { History } from './components/History/History.tsx'
import { Online } from './components/Online/Online.tsx'
import { Clock } from './components/Clock/Clock.tsx'
import { Logo } from '../Logo/Logo.tsx'
import { LangSwitcher } from '../LangSwitcher/LangSwitcher.tsx'

export const Footer = () => {
  return (
    <footer className="footer">
      <div className="footer__part footer__part_tleft">
        <Balance className="footer__item" />
        <Cashier className="footer__item" />
        <Home className="footer__item" />
      </div>
      <div className="footer__part footer__part_right">
        <Online className="footer__item" />
        <History className="footer__item" />
        <LangSwitcher className="footer__item" />
        <Clock className="footer__item" />
      </div>
      <div className="footer__logo">
        <Logo />
      </div>
    </footer>
  )
}
