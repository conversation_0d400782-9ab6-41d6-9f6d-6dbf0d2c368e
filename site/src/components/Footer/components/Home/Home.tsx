import './Home.scss'
import { Icon } from '../../../Icon/Icon.tsx'
import { IconType } from '../../../Icon/types.ts'
import { useTranslate } from '@/hooks/useTranslate.ts'
import type { MouseEvent } from 'react'
import { useStore } from '@/hooks/useStore.ts'

interface Props {
  className?: string
}

export const Home = ({ className }: Props) => {
  const { t } = useTranslate()
  const homeButton = useStore(state => state.homeButton)

  const handleHomeButton = (e: MouseEvent) => {
    e.stopPropagation()
    homeButton?.open()
  }

  if (!homeButton?.showOnDesktop) {
    return null
  }
  return (
    <div className={`home ${className ?? ''}`} onClick={handleHomeButton}>
      <Icon className="home__icon" type={IconType.Home} />
      <div className="home__label">{t('LEFT_MENU.home')}</div>
    </div>
  )
}
