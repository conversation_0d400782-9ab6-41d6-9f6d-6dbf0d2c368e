import './Online.scss'
import { useTranslate } from '@/hooks/useTranslate.ts'

interface Props {
  className?: string
}

export const Online = ({ className }: Props) => {
  const { t } = useTranslate()
  return (
    <div className={`online ${className ?? ''}`}>
      <div className="online__label">{t('PAGE_CONTENT.onAir')}</div>
      <div className="online__icon online__icon_highlighted" />
    </div>
  )
}
