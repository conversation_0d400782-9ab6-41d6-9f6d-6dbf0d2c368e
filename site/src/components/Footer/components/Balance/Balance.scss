@use '../../../../styles/variables';

.balance {
  display: flex;
  justify-content: left;
  align-items: center;
  line-height: 1.2;
  font-size: 14px;
  padding: 0 10px;

  &__label {
    padding-right: 10px;
    white-space: nowrap;
  }

  &__value {
    color: #fff;
  }

  .mobile & {
    position: absolute;
    left: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 11px;

    &__label {
      white-space: nowrap;
      color: #fff;
      padding-right: 0;
    }

    &__value {
      color: variables.$color-highlight;
    }
  }
}

