import './Balance.scss'
import { useTranslate } from '@/hooks/useTranslate.ts'
import { useStore } from '@/hooks/useStore.ts'
import { getMoneyFormatter } from '@/services/moneyFormatter.ts'

interface Props {
  className?: string
}

export const Balance = ({ className }: Props) => {
  const { t } = useTranslate()
  const playerInfo = useStore(state => state.playerInfo)

  if (playerInfo?.balance === undefined) {
    return null
  }
  return (
    <div className={`balance ${className ?? ''}`}>
      <div className="balance__label">{t('PAGE_CONTENT.balance')}:</div>
      <div
        className="balance__value">{getMoneyFormatter().formatNumber(playerInfo.balance, { showCurrency: true })}</div>
    </div>
  )
}
