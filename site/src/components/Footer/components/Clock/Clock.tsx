import { useEffect, useState } from 'react'
import './Clock.scss'

interface Props {
  className?: string
}

export const Clock = ({ className }: Props) => {
  const [value, setValue] = useState<Date>(new Date())

  useEffect(() => {
    const msUntilNextMinute = 60000 - (Date.now() % 60000)
    const timerId = window.setInterval(() => {
      setValue(new Date())
    }, msUntilNextMinute)

    return () => {
      if (timerId) {
        window.clearInterval(timerId)
      }
    }
  }, [])

  return (
    <div className={`clock ${className ?? ''}`}>
      {value?.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
    </div>
  )
}
