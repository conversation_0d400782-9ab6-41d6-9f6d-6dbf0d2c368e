import { Icon } from '../../../Icon/Icon.tsx'
import { IconType } from '../../../Icon/types.ts'
import './History.scss'
import { useHistoryUrl } from '@/hooks/useHistoryUrl.ts'

interface Props {
  className?: string
}

export const History = ({ className }: Props) => {
  const historyUrl = useHistoryUrl()

  if (!historyUrl) {
    return null
  }
  return (
    <a className={`history ${className ?? ''}`} href={historyUrl.href} target="_blank" rel="noreferrer">
      <Icon className="history__icon" type={IconType.History} />
    </a>
  )
}
