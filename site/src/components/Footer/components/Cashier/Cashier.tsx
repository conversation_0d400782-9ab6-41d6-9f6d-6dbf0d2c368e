import { Icon } from '../../../Icon/Icon.tsx'
import { IconType } from '../../../Icon/types.ts'
import { useTranslate } from '@/hooks/useTranslate.ts'
import './Cashier.scss'
import { buildCashierUrl } from '@/utils/cashierUrl.ts'
import { useStore } from '@/hooks/useStore.ts'

interface Props {
  className?: string
}

export const Cashier = ({ className }: Props) => {
  const cashierUrl = useStore(state => state.lobbyOptions?.cashierUrl)
  const { t, language } = useTranslate()

  const handleOpen = () => {
    if (cashierUrl) {
      window.open(buildCashierUrl(cashierUrl, { language }), '_blank', 'location=no width=820, height=820')
    }
  }

  if (cashierUrl) {
    return (
      <div onClick={handleOpen} className={`cashier ${className ?? ''}`}>
        <Icon className="cashier__icon" type={IconType.Plus} />
        <span className="cashier__label">{t('PAGE_CONTENT.cashier')}</span>
      </div>
    )
  }
  return null
}
