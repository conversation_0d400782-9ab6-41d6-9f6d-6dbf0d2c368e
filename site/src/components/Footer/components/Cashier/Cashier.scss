@use '../../../../styles/variables';

.cashier {
  display: flex;
  justify-content: left;
  align-items: center;
  line-height: 20px;
  font-size: 14px;
  cursor: pointer;
  padding: 0 10px;

  &__label {
    padding-left: 10px;
    white-space: nowrap;
    text-transform: uppercase;
    color: white;
  }

  &__icon {
    display: block;
    width: 16px;
    height: 16px;
    fill: variables.$color-highlight;
  }

  .mobile & {
    display: none;
  }
}
