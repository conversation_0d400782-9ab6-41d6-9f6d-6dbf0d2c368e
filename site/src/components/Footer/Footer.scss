@use '../../styles/variables';

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-grow: 0;
  flex-shrink: 0;
  height: 43px;
  padding: 0 10px;
  font-size: 14px;
  line-height: 14px;
  color: #989898;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  background: linear-gradient(to bottom,#272727 0%,#1c1c1c 50%,#141414 100%);

  &__part {
    display: flex;
    height: 100%;
  }

  &__logo {
    display: none;
    height: 25px;
    width: auto;
    position: absolute;
    margin-left: 50%;
    transform: translateX(-50%);

    img {
      height: 100%;
    }
  }

  &__item {
    position: relative;
    height: 100%;

    &:not(:last-child) {
      &::after {
        content: "";
        position: absolute;
        right: 0;
        top: 50%;
        display: block;
        height: 22px;
        width: 1px;
        margin-top: -11px;
        background-color: rgba(121, 121, 121, 0.3019607843);
      }
    }
  }

  .mobile & {
    height: 32px;
    background: linear-gradient(to bottom, rgba(248, 80, 50, 0) 0%, rgba(0, 0, 0, .51) 21%, rgb(0, 0, 0) 100%);

    &__logo {
      display: block;
    }

    &__item {
      &:not(:last-child) {
        &::after {
          display: none;
        }
      }
    }
  }
}
