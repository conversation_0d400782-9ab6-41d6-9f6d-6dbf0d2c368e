import type { PropsWithChildren } from 'react'
import { useCallback, useState } from 'react'
import type { TranslateStoreType } from '../contexts/TranslateContext.ts'
import { TranslateContext } from '../contexts/TranslateContext.ts'
import storage from '../utils/storage.ts'
import { DEFAULT_LANGUAGE, LANGUAGE_CODES, TRANSLATIONS } from '../services/translations.ts'
import { get } from 'lodash-es'

const KEY_LANGUAGE = 'language'

function parseLanguage(language: string) {
  let lang = language.toLowerCase()
  if (lang in LANGUAGE_CODES) {
    return LANGUAGE_CODES[lang]
  }
  if (lang.includes('_')) {
    lang = lang.replace('_', '-')
  }
  if (lang in LANGUAGE_CODES) {
    return LANGUAGE_CODES[lang]
  }
  const code = Object.keys(LANGUAGE_CODES).find(key => lang.startsWith(key))
  return code ? LANGUAGE_CODES[code] : null
}

function loadLanguage() {
  let language = storage.getItem(KEY_LANGUAGE)
  if (language) {
    language = parseLanguage(language)
  }
  return language ?? DEFAULT_LANGUAGE
}

export const TranslateStore = ({ children }: PropsWithChildren) => {
  const [language, setLanguage] = useState<string>(loadLanguage)

  const saveLanguage = useCallback((value: string) => {
    storage.setItem(KEY_LANGUAGE, value)
    setLanguage(value)
  }, [])

  const props: TranslateStoreType = {
    language,
    saveLanguage,
    detectLanguage: useCallback((initiator: string, queryLang?: string | null) => {
      console.info(`[lobby] Found languages at [${initiator}] - query:[${queryLang}], local:[${language}]`)
      if (queryLang) {
        const lng = parseLanguage(queryLang)
        if (lng) {
          saveLanguage(lng)
          return lng
        }
      }
      return loadLanguage()
    }, [language, saveLanguage]),
    t: useCallback((path: string, params?: Record<string, string>) => {
      const data = TRANSLATIONS[language] ?? TRANSLATIONS[DEFAULT_LANGUAGE]
      let record = get(data.translations, path)
      if (typeof record === 'string') {
        if (params) {
          for (const [name, value] of Object.entries(params)) {
            record = record.replace(new RegExp(`{{${name}}}`, 'g'), value)
          }
        }
        return record
      }
      return path
    }, [language]),
    translate: useCallback(function <T>(data?: Record<string, T>) {
      return data?.[language] ?? data?.[DEFAULT_LANGUAGE]
    }, [language])
  }
  return (
    <TranslateContext.Provider value={props}>{children}</TranslateContext.Provider>
  )
}
