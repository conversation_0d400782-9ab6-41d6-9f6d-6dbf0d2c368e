import { Modal } from '../Modal/Modal.tsx'
import { Languages } from '../Languages/Languages.tsx'
import './LanguageModal.scss'
import { useTranslate } from '@/hooks/useTranslate.ts'
import { useStore } from '@/hooks/useStore.ts'


export const LanguageModal = () => {
  const setOpenLanguageModal = useStore(state => state.setOpenLanguageModal)
  const isOpenLanguageModal = useStore(state => state.isOpenLanguageModal)
  const { language, saveLanguage } = useTranslate()

  const handleSelect = (code: string) => {
    saveLanguage(code)
    setOpenLanguageModal(false)
  }

  return (
    <Modal isOpen={isOpenLanguageModal} onClose={() => setOpenLanguageModal(false)} showCloseButton={true}>
      <div className="languages-modal">
        <Languages activeItemId={language} onSelect={handleSelect} />
      </div>
    </Modal>
  )
}
