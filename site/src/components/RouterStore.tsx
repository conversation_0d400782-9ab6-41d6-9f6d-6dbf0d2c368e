import type { PropsWithChildren } from 'react'
import { useCallback, useEffect, useState } from 'react'
import type { NavigationExtras } from '../models/router.ts'
import type { Route, RouterStoreType } from '../contexts/RouterStoreContext.ts'
import { RouterStoreContext } from '../contexts/RouterStoreContext.ts'
import { matchPath, matchPaths } from '@/utils/router.ts'

function matchRoute(currentPath: string) {
  const [isPlayGame, { gameCode }] = matchPaths(['/play/:gameCode', '/direct-launch/:gameCode'], currentPath)
  const route: Route = { gameCode }
  if (!isPlayGame) {
    const [isExternalToken, { ticket }] = matchPath('/ticket/:ticket', currentPath)
    route.ticket = ticket
    if (!isExternalToken) {
      const [, { name }] = matchPath('/:name', currentPath)
      route.name = name ?? ''
    }
  }
  return route
}

function getLocationPath() {
  const { hash, pathname } = location
  if (hash.startsWith('#/direct-launch/') || hash.startsWith('#/ticket/')) {
    return hash.slice(1).split('?')[0]
  }
  return pathname
}

function getLocationSearchParams() {
  const { hash, search } = location
  if (hash.startsWith('#/direct-launch/') || hash.startsWith('#/ticket/')) {
    return hash.slice(1).split('?')[1]
  }
  return search
}

export const RouterStore = ({ children }: PropsWithChildren) => {
  const [currentPath, setCurrentPath] = useState(getLocationPath())
  const [searchParams, setSearchParams] = useState(new URLSearchParams(getLocationSearchParams()))
  const [route, setRoute] = useState<Route>(matchRoute(getLocationPath()))

  useEffect(() => {
    const handleLocationChange = () => {
      const path = getLocationPath()
      setCurrentPath(path)
      setRoute(matchRoute(path))
      setSearchParams(new URLSearchParams(getLocationSearchParams()))
    }
    window.addEventListener('popstate', handleLocationChange)
    return () => {
      window.removeEventListener('popstate', handleLocationChange)
    }
  }, [])

  const navigate = useCallback((path = currentPath, extras?: NavigationExtras) => {
    let url = `${(path.startsWith('/') ? path : `/${path}`)}`
    if (extras?.searchParams) {
      const query = extras.searchParams.toString()
      if (query) {
        url = `${url}?${query}`
      }
    }
    if (extras?.replace) {
      window.history.replaceState({}, '', url)
    } else {
      window.history.pushState({}, '', url)
    }
    window.dispatchEvent(new PopStateEvent('popstate'))
  }, [currentPath])

  const props: RouterStoreType = {
    currentPath,
    searchParams,
    navigate,
    route
  }

  return (
    <RouterStoreContext.Provider value={props}>{children}</RouterStoreContext.Provider>
  )
}
