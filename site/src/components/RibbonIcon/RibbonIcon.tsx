import type { Ribbon } from '@/models/lobby.ts'
import './RibbonIcon.scss'
import { useTranslate } from '@/hooks/useTranslate.ts'

interface Props {
  ribbon?: Record<string, Ribbon>
  className?: string
}

export const RibbonIcon = ({ ribbon, className }: Props) => {
  const { translate } = useTranslate()
  const icon = translate(ribbon)
  if (!icon) {
    return null
  }
  return <div className={`ribbon-icon ${className ?? ''}`}
              style={{ background: icon.bg, color: icon.color }}>{icon.text}</div>
}
