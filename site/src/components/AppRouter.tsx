import { getBooleanParam } from '../utils/router.ts'
import { useEffect } from 'react'
import { setPlayerToken } from '../utils/playerInfo.ts'
import { PlayGame } from './PlayGame/PlayGame.tsx'
import { useRouterStore } from '../hooks/useRouterStore.ts'
import { useTranslate } from '../hooks/useTranslate.ts'
import { useErrorHandling } from '@/hooks/useErrorHandling.ts'
import { HomePage } from './HomePage/HomePage.tsx'
import { HomeButton } from '../services/homeButton.ts'
import { setLobbyId } from '../utils/lobbyId.ts'
import { useStore } from '@/hooks/useStore.ts'
import { LiveManagerSocketProvider } from '@/components/LiveManagerSocketProvider.tsx'
import { PlayerSocketProvider } from '@/components/PlayerSocketProvider.tsx'
import { getTerminalApi } from '@/services/terminalApi.ts'

export const AppRouter = () => {
  const { navigate, searchParams, route } = useRouterStore()
  const { handleError } = useErrorHandling()
  const { detectLanguage } = useTranslate()
  const openActiveGame = useStore((state) => state.openActiveGame)
  const setActiveGame = useStore((state) => state.setActiveGame)
  const setHomeButton = useStore(state => state.setHomeButton)

  useEffect(() => {
    if (route.gameCode) {
      const token = searchParams.get('token')
      const url = searchParams.get('url')
      if (url && token) {
        setLobbyId(searchParams.get('lobbyId'))
        const language = detectLanguage('direct-launch', searchParams.get('language') ?? searchParams.get('lang'))
        const performance = searchParams.get('performance') === 'true'
        if (!performance) {
          navigate(`play/${route.gameCode}`, {
            replace: true,
            searchParams: new URLSearchParams()
          })
        }
        setHomeButton(new HomeButton(
          getBooleanParam(searchParams.get('showDesktopHomeButton')),
          getBooleanParam(searchParams.get('swmpHomeButton')),
          searchParams.get('lobbyUrl') ?? searchParams.get('lobby')
        ))
        setPlayerToken(token)
        setActiveGame(route.gameCode, language, url)
      } else {
        const language = detectLanguage('play', searchParams.get('language') ?? searchParams.get('lang'))
        openActiveGame(route.gameCode, language).catch(handleError)
      }
    }
    if (route.ticket) {
      let lang = searchParams.get('language') ?? searchParams.get('lang')
      let ticket = route.ticket
      if (ticket?.includes('_lang_')) {
        [ticket, lang] = ticket.split('_lang_', 2)
      }
      const language = detectLanguage('ticket', lang)
      setHomeButton(new HomeButton(
        getBooleanParam(searchParams.get('showDesktopHomeButton')),
        getBooleanParam(searchParams.get('swmpHomeButton')),
        searchParams.get('lobbyUrl') ?? searchParams.get('lobby')
      ))
      getTerminalApi().externalLogin(ticket).then(response => {
        setLobbyId(response?.lobbyId ?? null)
        setPlayerToken(response?.token ?? null)
        const gameCode = searchParams.get('swGameCode')
        const category = searchParams.get('category')
        if (gameCode) {
          navigate(`play/${gameCode}`, {
            replace: true,
            searchParams: new URLSearchParams()
          })
          openActiveGame(gameCode, language).catch(handleError)
        } else {
          navigate(category ?? '', {
            replace: true,
            searchParams: new URLSearchParams()
          })
        }
      }).catch(handleError)
    }
  }, [detectLanguage, handleError, navigate, openActiveGame, route.gameCode, route.ticket, searchParams, setActiveGame, setHomeButton])

  return (
    <PlayerSocketProvider>
      <LiveManagerSocketProvider>
        {route.name !== undefined && <HomePage name={route.name} />}
        <PlayGame />
      </LiveManagerSocketProvider>
    </PlayerSocketProvider>
  )
}
