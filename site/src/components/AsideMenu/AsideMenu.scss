.aside {
  $this: &;

  position: fixed;
  z-index: 999;
  left: 0;
  top: 0;
  width: 0;
  height: 0;
  background-color: rgba(0, 0, 0, 0);
  overflow: hidden;

  &_open {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .6);
    transition: background-color 0.3s ease;

    #{$this}__container {
      transform: translate3d(0, 0, 0);
      transition: transform 0.3s ease;
    }
  }

  &__container {
    width: 211px;
    height: 100%;
    padding: 28px 0;
    overflow: auto;
    background-color: rgba(0, 0, 0, .75);
    transform: translate3d(-100%, 0 , 0);
  }

  &__item {
    display: flex;
    align-items: center;
    height: 54px;
    padding: 0 20px;
    color: #fff;
    text-decoration: none;
    cursor: pointer;
  }

  &__icon {
    width: 24px;
    height: 24px;
    margin-right: 15px;
    fill: #fff;
  }

  &__title {

  }
}
