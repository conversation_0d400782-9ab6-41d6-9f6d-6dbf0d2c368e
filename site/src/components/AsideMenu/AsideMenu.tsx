import type { MouseEvent } from 'react'
import { Icon } from '../Icon/Icon.tsx'
import { IconType } from '../Icon/types.ts'
import './AsideMenu.scss'
import { useHistoryUrl } from '@/hooks/useHistoryUrl.ts'
import { useTranslate } from '@/hooks/useTranslate.ts'
import { buildCashierUrl } from '@/utils/cashierUrl.ts'
import { useStore } from '@/hooks/useStore.ts'

export const AsideMenu = () => {
  const setIsNicknameModalOpen = useStore(state => state.setIsNicknameModalOpen)
  const homeButton = useStore(state => state.homeButton)
  const cashierUrl = useStore(state => state.lobbyOptions?.cashierUrl)
  const isOpenAsideMenu = useStore(state => state.isOpenAsideMenu)
  const setOpenAsideMenu = useStore(state => state.setOpenAsideMenu)
  const setOpenLanguageModal = useStore(state => state.setOpenLanguageModal)
  const { t, language } = useTranslate()
  const historyUrl = useHistoryUrl()

  const handleHomeButton = (e: MouseEvent) => {
    e.stopPropagation()
    homeButton?.open()
  }

  const handleCashier = (e: MouseEvent) => {
    e.stopPropagation()
    if (cashierUrl) {
      window.open(buildCashierUrl(cashierUrl, { language }), '_blank', 'location=no width=820, height=820')
    }
  }

  const handleBackdropClick = (e: MouseEvent) => {
    e.stopPropagation()
    setOpenAsideMenu(false)
  }

  const handleLanguageSettingsClick = (e: MouseEvent) => {
    e.stopPropagation()
    setOpenAsideMenu(false)
    setOpenLanguageModal(true)
  }
  
  const handleNicknameClick = (e: MouseEvent) => {
    e.stopPropagation()
    setIsNicknameModalOpen(true)
    setOpenAsideMenu(false)
  }

  return (
    <aside className={`aside ${isOpenAsideMenu ? 'aside_open' : ''}`} onClick={handleBackdropClick}>
      <nav className={'aside__container'}>
        {homeButton?.showOnMobile && <div className="aside__item" onClick={handleHomeButton}>
          <Icon className="aside__icon" type={IconType.Home} />
          <span className="aside__title">{t('LEFT_MENU.home')}</span>
        </div>}
        {cashierUrl && <div className="aside__item" onClick={handleCashier}>
          <Icon className="aside__icon" type={IconType.Cashier} />
          <span className="aside__title">{t('LEFT_MENU.cashier')}</span>
        </div>}
        <div className="aside__item" onClick={handleLanguageSettingsClick}>
          <Icon className="aside__icon" type={IconType.Language} />
          <span className="aside__title">{t('LEFT_MENU.languageSettings')}</span>
        </div>
        {historyUrl && <a className="aside__item" href={historyUrl.href} target="_blank" rel="noreferrer">
          <Icon className="aside__icon" type={IconType.History} />
          <span className="aside__title">{t('LEFT_MENU.history')}</span>
        </a>}
        <div className="aside__item" onClick={handleNicknameClick}>
          <Icon className="aside__icon" type={IconType.Nickname} />
          <span className="aside__title">{t('LEFT_MENU.nickname')}</span>
        </div>
      </nav>
    </aside>
  )
}
