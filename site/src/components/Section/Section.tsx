import type { SectionItem } from '@/models/lobby.ts'
import { ThumbList } from '@/components/ThumbList/ThumbList.tsx'
import { FavoriteSection } from '@/components/Section/FavoriteSection.tsx'
import { RecentSection } from '@/components/Section/RecentSection.tsx'

interface Props {
  section: SectionItem
  isGridLayout?: boolean
}

export const Section = ({ section, isGridLayout }: Props) => {
  if (section.showFavoriteGames) {
    return <FavoriteSection section={section} isGridLayout={isGridLayout} />
  }
  if (section.showRecentGames) {
    return <RecentSection section={section} isGridLayout={isGridLayout} />
  }
  return <ThumbList title={section.title} games={section.games} isGridLayout={isGridLayout} />
}
