import type { SectionItem } from '@/models/lobby.ts'
import '../ThumbList/ThumbList.scss'
import { ThumbList } from '@/components/ThumbList/ThumbList.tsx'
import { useStore } from '@/hooks/useStore.ts'

interface Props {
  section: SectionItem
  isGridLayout?: boolean
}

export const FavoriteSection = ({ section, isGridLayout }: Props) => {
  const favoriteGames = useStore(state => state.favoriteGames)
  return <ThumbList title={section.title} games={favoriteGames} isFavorites={true} isGridLayout={isGridLayout} />
}
