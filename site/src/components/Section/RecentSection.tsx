import type { SectionItem } from '@/models/lobby.ts'
import '../ThumbList/ThumbList.scss'
import { ThumbList } from '@/components/ThumbList/ThumbList.tsx'
import { useStore } from '@/hooks/useStore.ts'

interface Props {
  section: SectionItem
  isGridLayout?: boolean
}

export const RecentSection = ({ section, isGridLayout }: Props) => {
  const recentGames = useStore(state => state.recentGames)
  return <ThumbList title={section.title} games={recentGames} isRecent={true} isGridLayout={isGridLayout} />
}
