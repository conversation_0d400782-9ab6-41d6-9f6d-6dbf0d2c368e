.lang-switcher {
  position: relative;
  display: flex;
  align-items: center;
  height: 100%;
  &__current {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: 0 10px;
    text-transform: uppercase;
    cursor: pointer;
    user-select: none;
  }
  &__dropdown {
    width: 230px;
    position: absolute;
    bottom: 100%;
    right: -30px;
    padding: 15px 0;
    background: rgba(0, 0, 0, 0.9);
    height: calc(80vh - 50px);
    overflow: hidden;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    transform: translateX(30px);
  }

  .mobile & {
    display: none;
  }
}
