import cn from 'classnames'
import { useState } from 'react'
import { Languages } from '../Languages/Languages.tsx'
import './LangSwitcher.scss'
import { useTranslate } from '@/hooks/useTranslate.ts'

interface Props {
  className?: string
}

export const LangSwitcher = ({ className }: Props) => {
  const { language, saveLanguage } = useTranslate()
  const [isOpen, setIsOpen] = useState<boolean>(false)

  const handleSelect = (code: string) => {
    saveLanguage(code)
    close()
  }

  const toggleDropdown = () => setIsOpen(!isOpen)
  const close = () => setIsOpen(false)

  return (
    <div className={cn('lang-switcher', className)} tabIndex={0} onBlur={close}>
      <div className="lang-switcher__current" onClick={toggleDropdown}>{language}</div>
      {isOpen && <div className="lang-switcher__dropdown">
        <Languages activeItemId={language} onSelect={handleSelect} />
      </div>}
    </div>
  )
}
