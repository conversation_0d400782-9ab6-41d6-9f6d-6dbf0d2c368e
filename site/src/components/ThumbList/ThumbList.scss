.section {
  $this: &;

  position: relative;
  padding-top: 1.75em;

  &__title {
    margin-bottom: 9px;
    padding: 0 20px;
    font-size: 14px;
    text-transform: uppercase;
    color: #ffcf03;
    word-break: break-all;
  }

  &__games {
    display: grid;
    grid-template-rows: auto;
    grid-gap: 1.25em;
    padding: 0 1.25em;

    @media (min-width: 500px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media (min-width: 1024px) {
      grid-template-columns: repeat(4, 1fr);
    }

    @media (min-width: 1280px) {
      grid-template-columns: repeat(5, 1fr);
    }

    @media (min-width: 1560px) {
      grid-template-columns: repeat(6, 1fr);
    }

    @media (min-width: 1820px) {
      grid-template-columns: repeat(7, 1fr);
    }

    @media (min-width: 2600px) {
      grid-template-columns: repeat(8, 1fr);
    }
  }

  .mobile & {
    padding-top: 1em;

    @media (orientation: portrait) {
      padding-left: 2px;
      padding-right: 2px;

      &_grid {
        #{$this}__games {
          grid-template-columns: repeat(2, 1fr);
          grid-gap: .5em;
          padding: 0 .5em;

          @media (min-width: 768px) {
            grid-template-columns: repeat(3, 1fr);
          }

          @media (min-width: 1024px) {
            grid-template-columns: repeat(4, 1fr);
          }
        }
      }

      &:not(&_grid) {
        #{$this}__games {
          grid-template-columns: 1fr;
          grid-gap: .375em;
          padding: 0;
          padding-left: 2px;
          padding-right: 2px;
        }
      }

    }

    @media (orientation: landscape) {
      &__games {
        grid-template-columns: repeat(3, 1fr);
        grid-gap: .75em;
        padding: 0 .75em;

        @media (min-width: 1024px) {
          grid-template-columns: repeat(4, 1fr);
        }
      }
    }
  }

  .desktop & {
    font-size: 4vw;

    @media (min-width: 500px) {
      font-size: 2.2vw;
    }

    @media (min-width: 768px) {
      font-size: 1.7vw;
    }

    @media (min-width: 1024px) {
      font-size: 1.27vw;
    }

    @media (min-width: 1280px) {
      font-size: 1.025vw;
    }

    @media (min-width: 1560px) {
      font-size: 0.85vw;
    }

    @media (min-width: 1820px) {
      font-size: 0.75vw;
    }

    @media (min-width: 2600px) {
      font-size: 0.65vw;
    }
  }
}
