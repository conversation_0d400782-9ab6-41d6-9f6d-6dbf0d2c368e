import { useTranslate } from '@/hooks/useTranslate.ts'
import './EmptyList.scss'

interface Props {
  isFavorites?: boolean
  isRecent?: boolean
}

export const EmptyList = ({ isFavorites, isRecent }: Props) => {
  const { t } = useTranslate()
  
  if (isRecent) {
    return null
  }
  
  if (isFavorites) {
    return (
      <div className="empty-favorites" />
    )
  }
  
  return (
    <div className="empty-list">
      {t('PAGE_CONTENT.empty')}
    </div>
  )
}
