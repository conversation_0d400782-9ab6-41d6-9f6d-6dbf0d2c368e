.empty-favorites {
  display: block;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;

  .desktop & {
    height: 15em;
    background-image: url('./images/favorite-landscape.png');
    background-size: auto 80%;
  }

  .mobile & {
    @media (orientation: portrait) {
      padding-top: 30%;
      background-image: url('./images/favorite-portrait.png');
      background-size: 100%;
    }

    @media (orientation: landscape) {
      height: 10em;
      background-image: url('./images/favorite-landscape.png');
      background-size: auto 80%;
    }
  }
}

.empty-list {
  display: flex;
  width: 100%;
  height: calc(100vh - 250px);
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 1em;
}
