import { Thumb } from '../Thumb/Thumb.tsx'
import type { SectionItem } from '@/models/lobby.ts'
import './ThumbList.scss'
import { useTranslate } from '@/hooks/useTranslate.ts'
import { useRefreshDealerPicture } from '@/hooks/useRefreshDealerPicture.ts'
import { useStore } from '@/hooks/useStore.ts'
import { useMemo } from 'react'
import { isGameOpen } from '@/utils/game.ts'
import type { GameLiveInfo } from '@/models/game.ts'
import { EmptyList } from '@/components/ThumbList/EmptyList/EmptyList.tsx'

interface Props {
  title: SectionItem['title']
  games: GameLiveInfo[]
  isFavorites?: boolean
  isRecent?: boolean
  isGridLayout?: boolean
}

export const ThumbList = ({ title, games, isFavorites, isRecent, isGridLayout }: Props) => {
  const { translate } = useTranslate()
  const statuses = useStore((state) => state.statuses)

  const activeGames = useMemo(() => {
    return games.filter((game) => isGameOpen(statuses)(game))
  }, [games, statuses])

  useRefreshDealerPicture(activeGames)

  return (
    <div className={`section ${isGridLayout ? 'section_grid' : ''}`}>
      <div className="section__title">{translate(title)}</div>
      {activeGames.length ?
        <div className="section__games">
          {activeGames.map(game => <Thumb key={game.code} game={game} isGridLayout={isGridLayout} />)}
        </div> :
        <EmptyList isFavorites={isFavorites} isRecent={isRecent} />
      }
    </div>
  )
}
