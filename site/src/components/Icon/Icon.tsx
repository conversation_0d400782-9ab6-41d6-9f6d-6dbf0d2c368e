import type { IconType } from './types'
import type { MouseEvent } from 'react'

interface Props {
  type: IconType
  onClick?: (e: MouseEvent) => void
  className?: string
}

export const Icon = ({ type, className, onClick }: Props) => {
  const handleClick = (e: MouseEvent) => {
    if (onClick) {
      onClick(e)
    }
  }
  return (
    <svg className={className} aria-hidden="true" onClick={e => handleClick(e)}>
      <use href={`#${type}`} />
    </svg>
  )
}
