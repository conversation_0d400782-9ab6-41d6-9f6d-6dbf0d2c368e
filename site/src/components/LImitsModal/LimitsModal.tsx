import { Modal } from '../Modal/Modal.tsx'
import { LimitsSelector } from '../LImitsSelector/LimitsSelector.tsx'
import './LimitsModal.scss'
import { useStore } from '@/hooks/useStore.ts'

export const LimitsModal = () => {
  const limitsActiveTable = useStore((state) => state.limitsActiveTable)
  const setLimitsActiveTable = useStore((state) => state.setLimitsActiveTable)

  if (!limitsActiveTable) {
    return null
  }

  const close = () => {
    setLimitsActiveTable(null)
  }

  return (
    <Modal isOpen={Boolean(limitsActiveTable)} onClose={close}>
      <LimitsSelector
        className="limits-modal-selector"
        table={limitsActiveTable}
        onClose={close}
      />
    </Modal>
  )
}
