import type { MouseEvent } from 'react'
import type { LiveTableInfo } from '@/models/game.ts'
import './LimitsSelector.scss'
import { Icon } from '../Icon/Icon.tsx'
import { IconType } from '../Icon/types.ts'
import { LimitsSelectorItem } from './components/LimitsSelectorItem.tsx'
import { useTranslate } from '@/hooks/useTranslate.ts'
import { useStore } from '@/hooks/useStore.ts'

interface Props {
  table: LiveTableInfo
  onClose: () => void
  className?: string
}

export const LimitsSelector = ({ table, onClose, className }: Props) => {
  const { t } = useTranslate()
  const updateLimitRange = useStore(state => state.updateLimitRange)
  const selectedName = useStore(state => state.limits.find(
    item => item.tableId === table.tableId && item.gameCode === table.gameCode
  )?.name)

  const handleCLoseClick = (e: MouseEvent) => {
    e.stopPropagation()
    onClose()
  }

  const onLimitSelect = (name: string) => {
    updateLimitRange({ tableId: table.tableId, gameCode: table.gameCode ?? '', name })
    onClose()
  }

  if (!table.limits?.length) {
    return null
  }
  return (
    <div className={`limits-selector ${className ?? ''}`}>
      <div className="limits-selector__title">{t('GAME_INFO.changeLimits')}</div>
      <div className="limits-selector__close" onClick={handleCLoseClick}>
        <Icon className="limits-selector__icon limits-selector__icon_close" type={IconType.Close} />
      </div>
      {table.limits.map((limit) =>
        <LimitsSelectorItem
          key={limit.name}
          limit={limit}
          selected={selectedName === limit.name}
          onSelect={onLimitSelect} />
      )}
    </div>
  )
}
