@use '../../../styles/variables';

.limits-selector-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 22%;
  background: rgb(0 0 0 / 30%);
  cursor: pointer;
  text-transform: uppercase;
  transition: color 100ms ease-in-out;

  &:not(:last-child) {
    margin-bottom: 4%;
  }

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      color: variables.$color-highlight;
    }
  }

  &_selected {
    color: variables.$color-highlight;
  }

  &__icon {
    width: 0.75em;
    height: 0.75em;
    fill: #fff;

    &_check {
      position: absolute;
      left: 1em;
      fill: variables.$color-highlight;
    }
  }
}

