import type { SyntheticEvent } from 'react'
import { Icon } from '../../Icon/Icon.tsx'
import { IconType } from '../../Icon/types.ts'
import type { GameLiveInfoLimit } from '@/models/game.ts'
import './LimitsSelectorItem.scss'
import { getMoneyFormatter } from '@/services/moneyFormatter.ts'
import { useDeviceStore } from '@/hooks/useDeviceStore.ts'

interface Props {
  limit: GameLiveInfoLimit
  selected: boolean
  onSelect: (limitName: string) => void
}

export const LimitsSelectorItem = ({ limit, selected, onSelect }: Props) => {
  const { isMobile } = useDeviceStore()

  const handleClick = (e: SyntheticEvent) => {
    e.stopPropagation()
    onSelect(limit.name)
  }

  return (
    <div
      className={`limits-selector-item ${selected ? 'limits-selector-item_selected' : ''}`}
      onClick={!isMobile ? (e => handleClick(e)) : undefined}
      onTouchEnd={isMobile ? (e => handleClick(e)) : undefined}
    >
      {selected &&
        <Icon className="limits-selector-item__icon limits-selector-item__icon_check" type={IconType.Check} />}
      {getMoneyFormatter().formatGameLimits(limit)}
    </div>
  )
}
