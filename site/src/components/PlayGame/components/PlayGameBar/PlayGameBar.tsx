import { Icon } from '@/components/Icon/Icon.tsx'
import { IconType } from '@/components/Icon/types.ts'
import type { SyntheticEvent } from 'react'
import './PlayGameBar.scss'

interface Props {
  title?: string
  onClose: () => void
  onCollapse: () => void
}

export const PlayGameBar = ({ title, onClose, onCollapse }: Props) => {
  const handleClose = (e: SyntheticEvent)=> {
    e.stopPropagation()
    onClose()
  }
  
  const handleCollapse = (e: SyntheticEvent) => {
    e.stopPropagation()
    onCollapse()
  }
  
  return (
    <div className="play-game-bar">
      <div className="play-game-bar__title">{title}</div>
      <button className="play-game-bar__button" onClick={e => handleCollapse(e)}>
        <Icon type={IconType.Collapse} />
      </button>
      <button className="play-game-bar__button" onClick={e => handleClose(e)}>
        <Icon type={IconType.Close} />
      </button>
    </div>
  )
}
