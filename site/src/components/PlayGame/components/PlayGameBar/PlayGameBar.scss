@use "../../../../styles/variables";
.play-game-bar {
  display: flex;
  align-items: center;
  flex: 0 0 20px;
  height: 20px;
  width: 100%;
  padding: 0 5px;
  background: linear-gradient(to bottom, #5a5b5d 0%, #1a1b1d 31%, #1a1b1d 100%);

  &__title {
    margin-right: auto;
    font-size: 12px;
    color: #fff;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  &__button {
    width: 20px;
    height: 20px;
    padding: 5px;
    border: none;
    outline: none;
    background: transparent;
    cursor: pointer;

    &:hover {
      svg {
        fill: variables.$color-highlight;
      }
    }

    svg {
      display: block;
      width: 100%;
      height: 100%;
      fill: #fff;
      transition: color .15s ease-in-out;
    }
  }
}
