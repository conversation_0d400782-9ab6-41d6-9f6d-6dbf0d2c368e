import type { MouseEvent } from 'react'
import { useCallback, useEffect } from 'react'
import type { MessageData } from '@/models/message.ts'
import { MessageType } from '@/models/message.ts'
import { Icon } from '../Icon/Icon.tsx'
import { IconType } from '../Icon/types.ts'
import './PlayGame.scss'
import { useRouterStore } from '@/hooks/useRouterStore.ts'
import { useStore } from '@/hooks/useStore.ts'
import { useDraggable } from '@/hooks/useDraggable.ts'
import { useDeviceStore } from '@/hooks/useDeviceStore.ts'
import { PlayGameCaption } from '@/components/PlayGame/components/PlayGameCaption/PlayGameCaption.tsx'
import { useErrorHandling } from '@/hooks/useErrorHandling.ts'
import { PlayGameBar } from '@/components/PlayGame/components/PlayGameBar/PlayGameBar.tsx'
import { useGameTitle } from '@/hooks/useGameTitle.ts'

export const PlayGame = () => {
  const { isMobile, isPortrait } = useDeviceStore()
  const { navigate } = useRouterStore()
  const menuItem = useStore((state) => state.menuItem)
  const activeGame = useStore((state) => state.activeGame)
  const minimizeActiveGame = useStore((state) => state.minimizeActiveGame)
  const expandActiveGame = useStore((state) => state.expandActiveGame)
  const closeActiveGame = useStore((state) => state.closeActiveGame)
  const loadLobbyInfo = useStore(state => state.loadLobbyInfo)
  const enableCloseGame = useStore((state) => state.lobbyOptions?.enableCloseGame)
  const { ref, isDragging } = useDraggable({ isMobile, isPortrait, isEnabled: !!activeGame?.minimized })
  const { handleError } = useErrorHandling()
  const gameTitle = useGameTitle({
    gameCode: activeGame?.code,
    title: activeGame?.title
  })

  const minimizeGame = useCallback(() => {
    minimizeActiveGame(() => {
      navigate(menuItem?.slug ?? '')
    })
  }, [menuItem, minimizeActiveGame, navigate])

  const expandGame = useCallback(() => {
    expandActiveGame((gameCode) => {
      navigate(`play/${gameCode}`)
    })
  }, [expandActiveGame, navigate])

  const closeGame = useCallback(() => {
    closeActiveGame(() => {
      navigate(menuItem?.slug ?? '')
    })
  }, [closeActiveGame, menuItem, navigate])

  useEffect(() => {
    const handler = (event: MessageEvent<MessageData>) => {
      if (event.data) {
        const { type, payload } = event.data
        switch (type) {
          case MessageType.OpenLobby:
            minimizeGame()
            break
          case MessageType.Close:
            closeGame()
            break
          case MessageType.OpenCashier:
          case MessageType.OpenGameHistory:
            if (typeof payload === 'string') {
              window.open(payload, '_blank', 'location=no')
            }
            break
          case MessageType.GameStartedLoading:
            loadLobbyInfo().catch(handleError)
            break
        }
      }
    }
    window.addEventListener('message', handler)
    return () => {
      window.removeEventListener('message', handler)
    }
  }, [closeGame, handleError, loadLobbyInfo, minimizeGame])

  const handleOverlayClick = (e: MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }

  const handleExpandClick = (e: MouseEvent) => {
    e.stopPropagation()
    expandGame()
  }

  const handleCloseClick = (e: MouseEvent) => {
    e.stopPropagation()
    closeGame()
  }

  if (!activeGame?.url || !activeGame.code) {
    return null
  }

  return (
    <div ref={ref}
         className={`
            game
            ${activeGame.isLive ? 'game_live' : 'game_slot'}
            ${activeGame.minimized ? 'game_minimized' : ''}
            ${isDragging ? 'game_dragging' : ''}
         `}>
      {!activeGame.isLive && !activeGame.minimized && <PlayGameBar title={gameTitle} onClose={closeGame} onCollapse={minimizeGame} />}
      <iframe key={activeGame?.code} className="game__iframe" src={activeGame.url} allowFullScreen={true}
              allow="autoplay *; fullscreen *" />
      {activeGame.minimized && <div className="game__overlay" onClick={handleOverlayClick}>
        <div className="game__control game__control_expand" onClick={handleExpandClick}>
          <Icon type={IconType.Expand} />
        </div>
        {enableCloseGame &&
          <div className="game__control game__control_close" onClick={handleCloseClick}>
            <Icon type={IconType.Close} />
          </div>}
        <PlayGameCaption title={gameTitle} />
      </div>}
    </div>
  )
}
