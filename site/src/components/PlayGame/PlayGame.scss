@use '../../styles/variables';

.game {
  $this: &;

  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 4;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  touch-action: none;

  &_dragging {
    cursor: grab;
  }

  &_minimized {
    border: 2px solid #ffcf03;
    border-radius: 0.25em;
    top: unset;
    left: unset;
    bottom: 66px;
    right: 22px;
    width: 21.43em;
    height: 15.125em;

    .mobile & {
      @media (orientation: portrait) {
        font-size: 4vw;
        bottom: variables.$bottom-panel-mobile-height;
        right: 1em;
        width: 9em;
        height: 16em;
      }

      @media (orientation: landscape) {
        font-size: 4vh;
        bottom: variables.$bottom-panel-mobile-height;
        right: 1em;
        width: 17em;
        height: 9.6em;
      }
    }

    .desktop & {
      font-size: 4vw;

      #{$this}__control {
        visibility: hidden;
      }

      &:hover {
        #{$this}__control {
          visibility: visible;
        }
      }

      @media (min-width: 500px) {
        font-size: 2.2vw;
      }

      @media (min-width: 768px) {
        font-size: 1.7vw;
      }

      @media (min-width: 1024px) {
        font-size: 1.27vw;
      }

      @media (min-width: 1280px) {
        font-size: 1.025vw;
      }

      @media (min-width: 1560px) {
        font-size: 0.85vw;
      }

      @media (min-width: 1820px) {
        font-size: 0.75vw;
      }

      @media (min-width: 2600px) {
        font-size: 0.65vw;
      }
    }
  }

  &__iframe {
    flex: 1;
    width: 100%;
    height: 100%;
    border: none;
    background: #000;
    user-select: none;
  }

  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  &__control {
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2em;
    height: 2em;
    cursor: pointer;

    svg {
      width: 1em;
      height: 1em;
    }

    &_expand {
      left: 0;
    }

    &_close {
      fill: #fff;
      right: 0;
    }
  }
}


