.header {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 3;
  display: grid;
  grid-template-columns: auto 1fr;
  grid-template-rows: 32px 1fr;
  flex-shrink: 0;
  flex-grow: 0;
  height: 108px;
  width: 100%;
  background: #000;
  overflow: hidden;

  &:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    display: block;
    height: 2px;
    width: 100%;
    background: #282a35;
  }

  &__logo {
    grid-row: 1/span 2;
    grid-column: 1;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    max-width: 220px;
    max-height: 100%;
    padding: 10px 20px;
    user-select: none;
  }

  &__nav {
    grid-row: 2;
    grid-column: 2;
    z-index: 1;
  }

  &__welcome {
    grid-row: 1;
    grid-column: 2;
    margin-left: auto;
    padding: 0 8px;
  }

  .mobile & {
    grid-template-rows: 1fr;
    grid-template-columns: 46px 1fr;
    height: 76px;

    &__nav {
      grid-row: 1;
      grid-column: 2;
    }

    &__burger {
      grid-column: 1;
      grid-row: 1;
    }

    &__logo {
      display: none;
    }
  }
}
