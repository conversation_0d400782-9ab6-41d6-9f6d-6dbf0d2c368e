import './HomePage.scss'
import { AsideMenu } from '../AsideMenu/AsideMenu'
import { Footer } from '../Footer/Footer.tsx'
import { LimitsModal } from '../LImitsModal/LimitsModal'
import { Header } from '../Header/Header'
import { LanguageModal } from '../LanguageModal/LanguageModal'
import { MenuPage } from '../MenuPage/MenuPage'
import { useInactivityWatcher } from '@/hooks/useInactivityWatcher'
import { useRefreshPlayerToken } from '@/hooks/useRefreshPlayerToken'
import { useStore } from '@/hooks/useStore'
import { NicknameModal } from '@/components/NicknameModal/NicknameModal'
import { useEffect } from 'react'
import { useErrorHandling } from '@/hooks/useErrorHandling.ts'

interface Props {
  name: string
}

export const HomePage = ({ name }: Props) => {
  const loadLobbyInfo = useStore((state) => state.loadLobbyInfo)
  const menuItem = useStore(state => state.menuItem)
  const { handleError } = useErrorHandling()

  useInactivityWatcher()
  useRefreshPlayerToken()

  useEffect(() => {
    loadLobbyInfo(name).catch(handleError)
  }, [handleError, loadLobbyInfo, name])

  return (
    <div className='page'>
      <Header />
      <AsideMenu />
      {menuItem && <MenuPage menuItem={menuItem} />}
      <Footer />
      <LimitsModal />
      <LanguageModal />
      <NicknameModal />
    </div>
  )
}

