import type { MouseEvent } from 'react'
import { useState } from 'react'
import './Thumb.scss'
import { CurrentLimit } from '@/components/Thumb/components/CurrentLimit/CurrentLimit.tsx'
import type { GameLiveInfo } from '@/models/game.ts'
import { BlackjackSeats } from './components/BlackjackSeats/BlackjackSeats.tsx'
import { LimitsSelector } from '../LImitsSelector/LimitsSelector.tsx'
import { useErrorHandling } from '@/hooks/useErrorHandling.ts'
import { Caption } from './components/Caption/Caption.tsx'
import { DealerArea } from '@/components/Thumb/components/DealerArea/DealerArea.tsx'
import { RibbonIcon } from '../RibbonIcon/RibbonIcon.tsx'
import { useTranslate } from '@/hooks/useTranslate.ts'
import { useRouterStore } from '@/hooks/useRouterStore.ts'
import { useDeviceStore } from '@/hooks/useDeviceStore.ts'
import { Scoreboard } from '@/components/Thumb/components/Scoreboard/Scoreboard.tsx'
import { Favorite } from '@/components/Thumb/components/Favorite/Favorite.tsx'
import { useStore } from '@/hooks/useStore.ts'
import { Spinner } from '@/components/Thumb/components/Spinner/Spinner.tsx'

interface Props {
  game: GameLiveInfo
  isGridLayout?: boolean
}

export const Thumb = ({ game, isGridLayout }: Props) => {
  const { handleError } = useErrorHandling()
  const { language } = useTranslate()
  const { isMobile, isPortrait } = useDeviceStore()
  const { navigate } = useRouterStore()
  const setLimitsActiveTable = useStore((state) => state.setLimitsActiveTable)
  const openActiveGame = useStore((state) => state.openActiveGame)
  const activeGame = useStore((state) => state.activeGame)
  const [isOpenLimitsList, setOpenLimitsList] = useState<boolean>(false)
  const [isLaunching, setLaunching] = useState(false)

  const handleClick = (e: MouseEvent) => {
    e.stopPropagation()
    setLaunching(true)
    openActiveGame(game.code, language, (replace) => {
      setLaunching(false)
      navigate(`play/${game.code}`, { replace })
    }).catch(handleError)
  }

  const handleLimitsEdit = () => {
    if (game.table) {
      if (isMobile) {
        setLimitsActiveTable({ ...game.table, gameCode: game.code })
      } else {
        setOpenLimitsList(true)
      }
    }
  }

  return (
    <div className={`thumb ${activeGame?.code === game.code ? 'thumb_active' : ''} ${isGridLayout ? 'thumb_grid' : ''}`} onClick={handleClick}>
      <DealerArea game={game} />
      <Favorite game={game} className="thumb__favorite" />
      <Caption game={game} className="thumb__caption" />
      {game.table && <CurrentLimit className="thumb__limit" table={game.table} gameCode={game.code} onLimitEdit={handleLimitsEdit} />}
      <RibbonIcon className="thumb__ribbon" ribbon={game.ribbon} />
      {game.table && <Scoreboard className="thumb__scoreboard" table={game.table} isExpandable={!(isMobile && isPortrait && !isGridLayout)} />}
      <BlackjackSeats table={game.table} game={game} className="thumb__scoreboard" />
      {(game.table && isOpenLimitsList) &&
        <LimitsSelector
          className="thumb__limits"
          table={{ ...game.table, gameCode: game.code }}
          onClose={() => setOpenLimitsList(false)}
        />}
      {isLaunching && <Spinner />}
    </div>
  )
}
