import type { GameLiveInfo, LiveTableInfo } from '@/models/game.ts'
import { SeatsList } from './components/SeatsList/SeatsList.tsx'
import { FullMessage } from './components/FullMessage/FullMessage.tsx'
import './BlackjackSeats.scss'
import { useStore } from '@/hooks/useStore.ts'
import { GameType } from 'sw-live-core'

interface Props {
  table?: LiveTableInfo
  game: GameLiveInfo
  className?: string
}

export const BlackjackSeats = ({ table, game, className }: Props) => {
  const data = useStore((state) => state.seatsInfos.find(item => item.tableId === table?.tableId))
  if (!table || data?.tableType !== GameType.BLACKJACK_SEVEN_SEAT) {
    return null
  }
  return (
    <div className={`blackjack-seats ${className}`}>
      <div className="blackjack-seats__container">
        {data.seatsInfo.haveVacantSeat ? <SeatsList table={table} game={game} /> : <FullMessage />}
      </div>
    </div>
  )
}
