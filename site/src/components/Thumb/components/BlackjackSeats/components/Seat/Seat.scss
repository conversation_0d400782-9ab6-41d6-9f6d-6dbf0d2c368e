.seat {
  $this: &;

  width: 1.6em;
  height: 1.6em;
  border: 1px solid;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000000;

  &_vacant {
    color: #09BD04;
    cursor: pointer;

    #{$this}__icon {
      fill: #09BD04;
    }
  }

  &_occupied {
    color: #606060;

    #{$this}__icon {
      fill: #606060;
    }
  }

  &_player {
    color: #975E14;

    #{$this}__icon {
      fill: #975E14;
    }
  }

  &_disabled {
    color: rgba(#09BD04, 0.2);

    #{$this}__icon {
      fill: rgba(#09BD04, 0.2);
    }
  }

  &__icon {
    width: 1em;
    height: 1em;
    fill: #09BD04;
  }
}
