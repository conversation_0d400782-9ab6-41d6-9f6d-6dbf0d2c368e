import { Icon } from '@/components/Icon/Icon.tsx'
import { IconType } from '@/components/Icon/types.ts'
import { useTranslate } from '@/hooks/useTranslate.ts'
import { BlackjackSeatType } from '@/models/game.ts'
import type { Blackjack } from 'sw-live-core'
import './Seat.scss'

interface Props {
  id: Blackjack.PlayerSeat
  type: BlackjackSeatType
  isSeatLimitReached: boolean
  toggleSeat: (id: Blackjack.PlayerSeat) => void
}

export const Seat = ({ id, type, isSeatLimitReached, toggleSeat }: Props) => {
  const { t } = useTranslate()
  const isDisabled = isSeatLimitReached && type === BlackjackSeatType.Vacant

  const handleClick = () => {
    if (type !== BlackjackSeatType.Vacant || isSeatLimitReached) {
      return
    }
    toggleSeat(id)
  }

  return (
    <div
      title={isDisabled ? '' : t(`GAME_INFO.SEAT.${type}`)}
      className={`seat seat_${type} ${isDisabled ? 'seat_disabled' : ''}`}
      onClick={handleClick}
    >
      <Icon className="seat__icon" type={IconType.Player} />
    </div>
  )
}
