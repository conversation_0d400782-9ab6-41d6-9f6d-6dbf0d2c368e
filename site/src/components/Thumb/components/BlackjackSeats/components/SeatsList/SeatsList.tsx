import type { GameLiveInfo, LiveTableInfo } from '@/models/game.ts'
import type { Blackjack } from 'sw-live-core'
import { Seat } from '@/components/Thumb/components/BlackjackSeats/components/Seat/Seat.tsx'
import { useStore } from '@/hooks/useStore.ts'
import { useErrorHandling } from '@/hooks/useErrorHandling.ts'

interface Props {
  table: LiveTableInfo
  game: GameLiveInfo
}

export const SeatsList = ({ table }: Props) => {
  const seatsInfo = useStore((state) => state.seatsInfos.find(item => item.tableId === table?.tableId)?.seatsInfo)
  const toggleSeatsInfo = useStore((state) => state.toggleSeatsInfo)
  const { handleError } = useErrorHandling()

  if (!seatsInfo) {
    return null
  }
  const { P1, P2, P3, P4, P5, P6, P7 } = seatsInfo.seats
  const isSeatLimitReached = table.multiSeatLimit !== undefined && seatsInfo.countPlayerSeats >= table.multiSeatLimit

  function handleSeatClick(seat: Blackjack.PlayerSeat) {
    toggleSeatsInfo(table, seat).catch(handleError)
  }

  return (
    <>
      <Seat id="P7" type={P7} isSeatLimitReached={isSeatLimitReached} toggleSeat={handleSeatClick} />
      <Seat id="P6" type={P6} isSeatLimitReached={isSeatLimitReached} toggleSeat={handleSeatClick} />
      <Seat id="P5" type={P5} isSeatLimitReached={isSeatLimitReached} toggleSeat={handleSeatClick} />
      <Seat id="P4" type={P4} isSeatLimitReached={isSeatLimitReached} toggleSeat={handleSeatClick} />
      <Seat id="P3" type={P3} isSeatLimitReached={isSeatLimitReached} toggleSeat={handleSeatClick} />
      <Seat id="P2" type={P2} isSeatLimitReached={isSeatLimitReached} toggleSeat={handleSeatClick} />
      <Seat id="P1" type={P1} isSeatLimitReached={isSeatLimitReached} toggleSeat={handleSeatClick} />
    </>
  )
}
