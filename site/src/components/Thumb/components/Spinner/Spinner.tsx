import { type MouseEvent } from 'react'
import './Spinner.scss'

export const Spinner = () => {

  const handleClick = (e: MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }

  return (
    <div className="spinner" onClick={handleClick}>
      <svg viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg">
        <circle className="path" cx="25" cy="25" r="20" fill="none" strokeWidth="5"></circle>
      </svg>
    </div>
  )
}
