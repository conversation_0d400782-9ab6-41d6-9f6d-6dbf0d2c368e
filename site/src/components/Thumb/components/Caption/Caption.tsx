import { Players } from '../Players/Players.tsx'
import type { GameLiveInfo } from '@/models/game.ts'
import './Caption.scss'
import { useTranslate } from '@/hooks/useTranslate.ts'
import { Flag } from './components/Flags/Flag.tsx'

interface Props {
  game: GameLiveInfo
  className?: string
}

export const Caption = ({ game: { code, title, language, table }, className }: Props) => {
  const { translate } = useTranslate()
  return (
    <div className={`caption ${className ?? ''}`}>
      {language && <Flag language={language} className="caption__flag" />}
      <div className="caption__title">{translate(title)}</div>
      {table && <Players table={table} gameCode={code} />}
    </div>
  )
}
