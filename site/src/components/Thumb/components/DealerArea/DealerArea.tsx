import { useStore } from '@/hooks/useStore.ts'
import type { GameLiveInfo } from '@/models/game.ts'
import { useTranslate } from '@/hooks/useTranslate.ts'
import { useMemo } from 'react'
import './DealerArea.scss'


interface Props {
  game: GameLiveInfo
}

export const DealerArea = ({ game }: Props) => {
  const { translate } = useTranslate()
  const src = useStore((state) => {
    const url = game.table?.tableId ? state.dealerPictures.get(game.table.tableId) : undefined
    return url ?? game.table?.dealer?.picture
  })

  const title = useMemo(() => translate(game.title), [game.title, translate])

  return (
    <div className="dealer-area">
      {game.overlayUrl && <img src={translate(game.overlayUrl)} alt={title} className="dealer-area__overlay" />}
      {src && <img className="dealer-area__picture" src={src} alt={title} />}
      {game.poster && <img className="dealer-area__picture" src={game.poster} alt={title} />}
    </div>
  )
}
