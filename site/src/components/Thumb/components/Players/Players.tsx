import './Players.scss'
import { Icon } from '../../../Icon/Icon.tsx'
import { IconType } from '../../../Icon/types.ts'
import type { LiveTableInfo } from '@/models/game.ts'
import { useStore } from '@/hooks/useStore.ts'
import { GameType } from 'sw-live-core'

interface Props {
  gameCode: string
  table: LiveTableInfo
  className?: string
}

export const Players = ({ gameCode, table, className }: Props) => {
  const numberOfPlayers = useStore((state) => state.lobbyOptions?.numberOfPlayers)
  const data = useStore((state) => state.numberOfPlayers.find(item => item.tableId === table.tableId))
  const seatsInfosData = useStore((state) => state.seatsInfos.find(item => item.tableId === table.tableId))

  if (!numberOfPlayers?.show) {
    return null
  }
  if (numberOfPlayers.games?.length && !numberOfPlayers.games.includes(gameCode)) {
    return null
  }

  if (seatsInfosData && seatsInfosData.tableType === GameType.BLACKJACK_SEVEN_SEAT) {
    return (
      <div className={`players ${className ?? ''}`}>
        <Icon className="players__icon" type={IconType.Player} />
        <div className="players__amount">{seatsInfosData.seatsInfo.countOccupiedSeats}/7</div>
      </div>
    )
  }
  if (data?.numberOfPlayers) {
    return (
      table.numberOfPlayers ? <div className={`players ${className ?? ''}`}>
        <Icon className="players__icon" type={IconType.Player} />
        <div className="players__amount">{data.numberOfPlayers}</div>
      </div> : null
    )
  }

  return null
}
