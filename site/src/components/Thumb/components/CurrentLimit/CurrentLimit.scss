@use "../../../../styles/variables";

.current-limit {
  $this: &;

  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  height: 1.35em;
  padding: 0 0.25em;
  font-size: 1.5em;
  border-bottom-right-radius: .25em;
  background: linear-gradient(to bottom, #000 0%, rgba(0, 0, 0, 0.7) 100%);

  &:hover {
    #{$this}__item {
      &_expand {
        display: none;
      }

      &_edit {
        display: flex;
      }

      &_max {
        display: flex;
        font-size: 0.875em;
        transition: font-size 0.1s ease-in-out;
      }
    }

  }

  &__item {
    display: flex;
    align-items: center;
    height: 100%;
    font-size: 0.875em;
    white-space: nowrap;
    color: variables.$color-highlight;;
    user-select: none;

    &_min {
      white-space: nowrap;
      text-transform: uppercase;
    }

    &_edit {
      display: none;
      align-items: center;
      justify-content: center;
      width: 1.5em;
      height: 1.5em;
      margin-right: -0.2em;
      cursor: pointer;
      transition: color 0.15s ease-in-out;

      &:hover {
        #{$this}__pencil {
          fill: variables.$color-highlight;
        }
      }
    }

    &_max {
      font-size: 0;
    }

    &_expand {
      display: block;
      width: 1em;
      margin-left: 0.25em;
      fill: variables.$color-highlight;
      transition: font-size 0.2s ease-in-out;
    }
  }

  &__pencil {
    fill: #606060;
    width: 0.75em;
    height: 0.75em;
  }

  .mobile & {
    &__pencil {
      fill: variables.$color-highlight;
    }
  }
}

.thumb {
  .desktop & {
    &:hover{
      .current-limit {
        &__item {
          &_expand {
            display: none;
          }

          &_edit {
            display: flex;
          }

          &_max {
            display: flex;
            font-size: 0.875em;
            transition: font-size 0.1s ease-in-out;
          }
        }
      }
    }
  }
}
