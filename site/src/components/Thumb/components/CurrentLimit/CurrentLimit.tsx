import type { MouseEvent } from 'react'
import { useMemo } from 'react'
import { Icon } from '../../../Icon/Icon.tsx'
import { IconType } from '../../../Icon/types.ts'
import './CurrentLimit.scss'
import type { LiveTableInfo } from '@/models/game.ts'
import { useStore } from '@/hooks/useStore.ts'
import { getMoneyFormatter } from '@/services/moneyFormatter.ts'

interface Props {
  table: LiveTableInfo
  gameCode: string
  onLimitEdit: () => void
  className?: string
}

export const CurrentLimit = ({ table, gameCode, onLimitEdit, className }: Props) => {
  const limitName = useStore(state => state.limits.find(
    item => item.tableId === table.tableId && item.gameCode === gameCode
  )?.name)

  const currentLimit = useMemo(() => {
    const limit = table.limits.find(({ name }) => name === limitName)
    if (limit) {
      const formatter = getMoneyFormatter()
      return {
        stakeMin: `${formatter.currencySymbol} ${formatter.formatNumber(limit.stakeMin)}`,
        stakeMax: formatter.formatNumber(limit.stakeMax)
      }
    }
  }, [limitName, table.limits])

  const isEditable = useMemo(() => (table.limits.length ?? 0) > 1, [table.limits.length])

  if (!currentLimit) {
    return null
  }

  const handleEditClick = (e: MouseEvent) => {
    e.stopPropagation()
    onLimitEdit()
  }

  return (
    <div className={`current-limit ${className ?? ''}`} onClick={e => e.stopPropagation()}>
      <div className="current-limit__item current-limit__item_min" onClick={e => e.stopPropagation()}>
        {currentLimit.stakeMin}
      </div>
      <Icon className="current-limit__item current-limit__item_expand" type={IconType.ChevronRight}
            onClick={e => e.stopPropagation()} />
      <div className="current-limit__item current-limit__item_max">- {currentLimit.stakeMax}</div>
      {isEditable && <div className="current-limit__item current-limit__item_edit" onClick={handleEditClick}>
        <Icon className="current-limit__pencil" type={IconType.Edit} />
      </div>}
    </div>
  )
}
