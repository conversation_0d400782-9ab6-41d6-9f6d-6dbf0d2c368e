import { Icon } from '@/components/Icon/Icon.tsx'
import { IconType } from '@/components/Icon/types.ts'
import type { MouseEvent } from 'react'
import { useCallback } from 'react'
import { useErrorHandling } from '@/hooks/useErrorHandling.ts'
import { useStore } from '@/hooks/useStore.ts'
import './Favorite.scss'
import type { GameLiveInfo } from '@/models/game.ts'

interface Props {
  game: GameLiveInfo
  className?: string
}

export const Favorite = ({ game, className }: Props) => {
  const { handleError } = useErrorHandling()
  const toggleFavoriteGame = useStore(state => state.toggleFavoriteGame)
  const hasFavorite = useStore(state => state.hasFavorite)
  const isFavorite = useStore(state => state.favoriteGames.some(({ code }) => code === game.code))

  const handleClick = useCallback((e: MouseEvent) => {
    e.stopPropagation()
    toggleFavoriteGame(game).catch(handleError)
  }, [game, handleError, toggleFavoriteGame])

  if (!hasFavorite) {
    return null
  }
  return (
    <button className={`favorite ${className ?? ''}`} onClick={handleClick}>
      <Icon className={`favorite__icon ${isFavorite ? 'favorite__icon_full' : 'favorite__icon_empty'}`}
            type={isFavorite ? IconType.StarFull : IconType.StarEmpty} />
    </button>
  )
}
