@use '../../../../styles/variables';

.favorite {
  background: transparent;
  border: none;
  width: 1.9em;
  height: 1.9em;
  padding: 0;
  cursor: pointer;
  font-size: 0.8em;
  grid-column: 1;
  grid-row: 1 / span 3;
  z-index: 2;
  justify-self: end;
  margin: 0.25em 0.25em 0 0;

  &__icon {
    width: 100%;
    height: 100%;
    fill: #fff;
    transition: all 0.15s ease-in-out;

    &_full {
      fill: variables.$color-highlight
    }

    &_empty {
      &:hover {
        fill: variables.$color-highlight
      }
    }

    &:hover {
      transform: scale(1.1);
    }
  }
}

.mobile.layout-list {
  .favorite {
    @media (orientation: portrait) {
      font-size: 0.7em;
      margin: 0.25em 0.25em 0 0;
    }
  }
}
