import type { LiveTableInfo } from '@/models/game.ts'
import { SwScoreboard } from 'sw-scoreboard'
import { useStore } from '@/hooks/useStore.ts'
import { GameType } from 'sw-live-core'

interface Props {
  table: LiveTableInfo
  isExpandable?: boolean
  className?: string
}

export const Scoreboard = ({ table, isExpandable, className }: Props) => {
  const data = useStore((state) => state.scoreboards.find(item => item.tableId === table.tableId))

  if (!data || data.tableType === GameType.BLACKJACK_SEVEN_SEAT) {
    return null
  }
  return <SwScoreboard className={className} payload={data.scoreboard} type={data.tableType} isExpandable={isExpandable} isAtom={table.isAtom} />
}
