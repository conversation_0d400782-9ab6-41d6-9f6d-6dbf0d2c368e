@use '../../styles/variables';
@use "sw-scoreboard/dist/index.scss";

.thumb {
  $this: &;
  $caption-height: 2em;
  $scoreboard-height: 19.5%;
  $ribbon_height: 1em;

  position: relative;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto $ribbon_height $scoreboard-height $caption-height;
  cursor: pointer;
  border: 2px solid transparent;
  border-radius: 0.2em;
  overflow: hidden;

  &_active {
    border-color: variables.$color-highlight;
  }

  &_grid {
    // Grid specific styles
  }

  &__scoreboard {
    grid-column: 1;
    grid-row: 3;
    position: absolute;
    bottom: 2em;
    z-index: 1;
    width: 100%;
  }

  &__ribbon {
    position: relative;
    top: -1em;
    z-index: 1;
    grid-column: 1;
    grid-row: 2;
    margin-right: auto;
    min-width: 10em;
  }

  &__limits {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    width: 100%;
    height: 100%;
  }

  .mobile &  {
    border-width: 1px;

    @media (orientation: portrait) {
      &_grid {
        font-size: 3vw;

        @media (min-width: 768px) {
          font-size: 2vw;
        }

        @media (min-width: 1024px) {
          font-size: 1.5vw;
        }
      }

      &:not(.thumb_grid) {
        $caption-height: 1.75em;
        $ribbon-height: 0.8em;

        font-size: 4vw;
        grid-template-columns: 6.9em 1fr;
        grid-template-rows: $caption-height 1fr $ribbon-height;
        border-radius: unset;

        .thumb__img {
          grid-row: 1 / span 3;
        }

        .thumb__ribbon {
          position: static;
          top: unset;
          grid-column: 1;
          grid-row: 3;
        }

        .thumb__scoreboard {
          grid-column: 2;
          grid-row: 2 / span 3;
          height: auto;
          position: relative;
          bottom: unset;
        }

        .thumb__caption {
          grid-column: 2;
          grid-row: 1;
          background: linear-gradient(to bottom, #212531 0%, rgba(11, 14, 25, 0) 100%);
          font-size: 0.75em;
        }

        .thumb__favorite {
          font-size: 0.7em;
          margin: 0.25em 0.25em 0 0;
        }

        .thumb__limit {
          font-size: 1em;
        }
      }
    }

    @media (orientation: landscape) {
      font-size: 2vw;

      @media (min-width: 1024px) {
        font-size: 1.5vw;
      }
    }
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}
