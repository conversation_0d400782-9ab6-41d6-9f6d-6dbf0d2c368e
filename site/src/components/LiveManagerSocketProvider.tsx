import { type ReactNode, useEffect, useRef } from 'react'
import type { LiveTableId } from '@/models/game'
import type { Socket } from 'socket.io-client'
import { io } from 'socket.io-client'
import type { LiveManagerEvents } from '@/models/socket'
import { TableStatus } from 'sw-live-core'
import { getPlayerToken } from '@/utils/playerInfo'
import { useStore } from '@/hooks/useStore'
import { LiveManagerSocketContext } from '@/contexts/LiveManagerSocketContext'

interface ClientEvents {
  'login': (data: { token: string }) => void
  'live-subscribe': (data: LiveTableId) => void
  'live-unsubscribe': (data: LiveTableId) => void
}

type LiveManagerSocket = Socket<LiveManagerEvents, ClientEvents>

interface Props {
  children: ReactNode
}

export function LiveManagerSocketProvider({ children }: Props) {
  const socketRef = useRef<LiveManagerSocket>(null)
  const tablesRef = useRef<Record<string, boolean>>({})
  const liveManagerEndpoint = useStore(state => state.liveManagerEndpoint)
  const setStatus = useStore(state => state.setStatus)
  const setScoreboard = useStore(state => state.setScoreboard)
  const updateSeatsInfo = useStore(state => state.updateSeatsInfo)
  const setSeatsInfo = useStore(state => state.setSeatsInfo)
  const setDealerInfo = useStore(state => state.setDealerInfo)
  const updateNumberOfPlayers = useStore(state => state.updateNumberOfPlayers)

  useEffect(() => {
    const token = getPlayerToken()
    if (!socketRef.current && token && liveManagerEndpoint) {
      const { url, path, socketPath } = liveManagerEndpoint
      const socket: LiveManagerSocket = io(url, {
        transports: ['websocket'],
        ...(path ? { path: `/${path}${socketPath}/socket.io` } : {}),
        query: {
          sw_ts: Date.now(),
          'sw_player_token': token
        }
      })

      socket.on('reconnect_attempt', () => {
        socket.io.opts.transports = ['polling', 'websocket']
      })
      socket.on('connect', () => {
        console.info('[lobby] live manager socket connected')
      })
      socket.on('disconnect', () => {
        console.info('[lobby] live manager socket disconnected')
      })

      socket.on('live-table', ({ tableId, tableType, status, result: scoreboard, dealer, numberOfPlayers, seats }) => {
        setStatus({ tableId, open: status === TableStatus.ONLINE })
        setScoreboard({ tableId, tableType, scoreboard })
        setDealerInfo({ tableId, dealer })
        setSeatsInfo(tableId, tableType, seats)
        updateNumberOfPlayers({ tableId, numberOfPlayers })
      })

      socket.on('live-table-open', ({ tableId, status }) => {
        setStatus({ tableId, open: status === TableStatus.ONLINE })
      })

      socket.on('live-table-scoreboard', ({ tableId, tableType, result: scoreboard }) => {
        setScoreboard({ tableId, tableType, scoreboard })
      })

      socket.on('live-table-dealer', setDealerInfo)

      socket.on('live-table-state', ({ tableId, status }) => {
        setStatus({ tableId, open: status === TableStatus.ONLINE })
      })

      socket.on('live-table-seat', updateSeatsInfo)
      socket.on('live-table-players', updateNumberOfPlayers)

      const tableIds: LiveTableId[] = []
      for (const [key, subscribed] of Object.entries(tablesRef.current)) {
        if (!subscribed) {
          const [provider, tableId] = key.split('-', 2)
          tableIds.push({ provider, tableId })
        }
      }
      if (tableIds.length) {
        for (const tableId of tableIds) {
          socket.emit('live-subscribe', tableId)
          tablesRef.current[`${tableId.provider}-${tableId.tableId}`] = true
        }
      }

      socketRef.current = socket

      return () => {
        const socket = socketRef.current
        if (socket?.connected) {
          socket.close()
        }
      }
    }
  }, [liveManagerEndpoint, setDealerInfo, setScoreboard, setSeatsInfo, setStatus, updateNumberOfPlayers, updateSeatsInfo])

  useEffect(() => {
    const onVisibilityChange = () => {
      const socket = socketRef.current
      if (socket) {
        if (document.hidden) {
          console.info('[lobby] Pause live tables subscription')
          if (socket.connected) {
            socket.close()
          }
        } else {
          console.info('[lobby] Resume live tables subscription')
          if (!socket.connected) {
            socket.open()
          }
        }
      }
    }

    document.addEventListener('visibilitychange', onVisibilityChange, false)
    return () => {
      document.removeEventListener('visibilitychange', onVisibilityChange)
    }
  }, [])

  const subscribe = (tableIds?: LiveTableId[]) => {
    const socket = socketRef.current
    const currentIds = Object.entries(tablesRef.current)
      .filter(([, subscribed]) => subscribed)
      .map(([key]) => key)
    const newIds = tableIds?.map(({ provider, tableId }) => `${provider}-${tableId}`) ?? []

    if (socket?.connected) {
      const unsubscribeIds = currentIds.filter(key => !newIds.includes(key))
      for (const key of unsubscribeIds) {
        const [provider, tableId] = key.split('-', 2)
        socket.emit('live-unsubscribe', { provider, tableId })
        delete tablesRef.current[key]
      }
    }
    const subscribeIds = newIds.filter(key => !currentIds.includes(key))
    if (subscribeIds.length) {
      for (const key of subscribeIds) {
        const [provider, tableId] = key.split('-', 2)
        let subscribed = false
        if (socket?.connected) {
          socket.emit('live-subscribe', { provider, tableId })
          subscribed = true
        }
        tablesRef.current[key] = subscribed
      }
    }
  }

  return (
    <LiveManagerSocketContext.Provider value={{ subscribe }}>
      {children}
    </LiveManagerSocketContext.Provider>
  )
}
