import { useMemo } from 'react'
import { useTranslate } from './useTranslate'
import { getPlayerToken } from '../utils/playerInfo.ts'
import { useStore } from '@/hooks/useStore.ts'

export const useHistoryUrl = () => {
  const playerInfo = useStore(state => state.playerInfo)
  const { language } = useTranslate()

  return useMemo(() => {
    if (!playerInfo?.historyUrl) {
      return
    }

    const api = new URL(window.location.href)
    api.pathname = '/api/player'
    api.search = ''
    api.hash = ''

    const url = new URL(playerInfo.historyUrl)
    url.searchParams.set('language', language)

    const playerToken = getPlayerToken()
    if (playerToken) {
      url.searchParams.set('playerToken', playerToken)
      url.searchParams.set('api', api.href)
      url.searchParams.set('currency', playerInfo.currency)
    }

    return url
  }, [playerInfo, language])
}
