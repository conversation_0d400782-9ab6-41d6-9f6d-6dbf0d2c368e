import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { ActiveGame, GameLiveDealer, GameLiveInfo, LiveTableInfo, SeatsInfo } from '@/models/game.ts'
import { BlackjackSeatType } from '@/models/game.ts'
import { getMenuItemGames, toSeatsInfo, toSeatType, toTableSeats } from '@/utils/game.ts'
import type { SwScoreboardPayloadType } from 'sw-scoreboard'
import type { Blackjack } from 'sw-live-core'
import { GameType } from 'sw-live-core'
import { uniq, uniqBy } from 'lodash-es'
import type { LiveManagerEndpoint, LobbyInfoOptions, MenuItem, SectionItem } from '@/models/lobby.ts'
import type { PlayerInfo } from '@/models/playerInfo.ts'
import { getPlayerApi } from '@/services/playerApi.ts'
import type { HomeButton } from '@/services/homeButton.ts'
import { getBooleanParam } from '@/utils/router.ts'
import { getLiveApi } from '@/services/tableApi.ts'
import { getPlayerToken, setPlayerToken } from '@/utils/playerInfo.ts'
import { getLobbyId } from '@/utils/lobbyId.ts'

export interface LiveTableStatus {
  tableId: string
  open: boolean
}

interface TableDealerInfo {
  tableId: string
  dealer: GameLiveDealer
}

interface TableScoreboard {
  tableId: string
  tableType: GameType
  scoreboard: SwScoreboardPayloadType
}

interface TableSeatsInfo {
  tableId: string
  tableType: GameType
  seatsInfo: SeatsInfo
}

interface TableSeatUpdate {
  tableId: string
  tableType: GameType
  seat: Blackjack.PlayerSeat | null
  previousSeat?: Blackjack.PlayerSeat
  playerCode: string;
}

interface TableNumberOfPlayers {
  tableId: string
  numberOfPlayers: number
}

interface TableLimitRange {
  tableId: string
  gameCode: string
  name: string
}

interface State {
  homeButton?: HomeButton | null
  setHomeButton: (homeButton: HomeButton | null) => void

  activeGame: ActiveGame | null
  setActiveGame: (gameCode: string, language: string, url: string, isLive?: boolean) => void
  minimizeActiveGame: (fn?: (gameCode: string) => void) => void
  expandActiveGame: (fn?: (gameCode: string) => void) => void
  closeActiveGame: (fn?: (gameCode: string) => void) => void
  openActiveGame: (gameCode: string, language: string, fn?: (expand: boolean) => void) => Promise<void>

  playerInfo: PlayerInfo | null
  setPlayerInfo: (playerInfo: PlayerInfo | null) => void
  changeNickname: (nickname: string) => Promise<PlayerInfo | undefined>

  loadLobbyInfo: (name?: string) => Promise<void>

  lobbyOptions: LobbyInfoOptions | null
  liveManagerEndpoint: LiveManagerEndpoint | null

  menuItems: MenuItem[]
  menuItem: MenuItem | null,

  dealerPictures: Map<string, string>
  refreshDealerPictures: (games: GameLiveInfo[]) => void

  statuses: LiveTableStatus[]
  setStatus: (data: LiveTableStatus) => void

  dealers: TableDealerInfo[]
  setDealerInfo: (data: TableDealerInfo) => void

  scoreboards: TableScoreboard[]
  setScoreboard: (data: TableScoreboard) => void

  seatsInfos: TableSeatsInfo[]
  setSeatsInfo: (tableId: TableSeatsInfo['tableId'], tableType: TableSeatsInfo['tableType'], seats?: Blackjack.SeatState[]) => void
  updateSeatsInfo: (data: TableSeatUpdate) => void
  toggleSeatsInfo: (table: LiveTableInfo, seat: Blackjack.PlayerSeat) => Promise<void>

  numberOfPlayers: TableNumberOfPlayers[]
  updateNumberOfPlayers: (data: TableNumberOfPlayers) => void

  limits: TableLimitRange[]
  updateLimitRange: (data: TableLimitRange) => void

  hasFavorite: boolean
  favoriteGames: GameLiveInfo[]
  setFavoriteGame: (gameCode: string, isFavorite?: boolean) => void
  toggleFavoriteGame: (game: GameLiveInfo) => Promise<void>

  recentGames: GameLiveInfo[]
  setRecentGame: (gameCode: string, time: number) => void

  isNicknameModalOpen: boolean
  setIsNicknameModalOpen: (open: boolean) => void

  isOpenAsideMenu: boolean
  setOpenAsideMenu: (open: boolean) => void

  isOpenLanguageModal: boolean
  setOpenLanguageModal: (open: boolean) => void

  limitsActiveTable: LiveTableInfo | null,
  setLimitsActiveTable: (limitsActiveTable: LiveTableInfo | null) => void
}

function updatePicture(picture?: string) {
  if (picture) {
    try {
      const url = new URL(picture)
      url.searchParams.set('t', `${new Date().getTime()}`)
      return url.href
    } catch {
      console.error(`[lobby] Failed image [${picture}]`)
    }
  }
}

function getSections(menuItems: MenuItem[], filterFn: (value: SectionItem) => boolean) {
  return menuItems
    .map(({ sections }) => sections)
    .filter((sections): sections is SectionItem[] => Array.isArray(sections))
    .flat()
    .filter(filterFn)
}

export const useStore = create<State>()(persist(((set, get) => ({
  playerInfo: null,
  setPlayerInfo: (playerInfo) => set({
    playerInfo
  }),
  changeNickname: async (nickname) => {
    const info = await getPlayerApi(getPlayerToken()).changeNickname(nickname)
    set(state => ({
      playerInfo: {
        ...(state.playerInfo ?? {} as PlayerInfo),
        ...(info ?? {})
      }
    }))
    return info
  },

  activeGame: null,
  setActiveGame: (gameCode, language, gameUrl) => {
    const url = new URL(gameUrl)
    url.searchParams.set('isLobby', '1')
    url.searchParams.set('modal', '1')
    if (language) {
      url.searchParams.set('lc', language)
      url.searchParams.set('language', language)
    }
    window.SW_LOBBY = {
      isGameMinimized: false
    }
    const isLive = getBooleanParam(url.searchParams.get('live'))

    set({
      activeGame: {
        code: gameCode,
        url: url.href,
        language,
        isLive
      }
    })
  },
  minimizeActiveGame: (fn) => {
    const { activeGame } = get()
    if (activeGame) {
      if (activeGame.minimized) {
        return
      }
      window.SW_LOBBY = {
        isGameMinimized: true
      }
      set({
        activeGame: {
          ...activeGame,
          minimized: true
        }
      })
      fn?.(activeGame.code)
    }
  },
  expandActiveGame: (fn) => {
    const { activeGame } = get()
    if (activeGame) {
      if (!activeGame.minimized) {
        return
      }
      window.SW_LOBBY = {
        isGameMinimized: false
      }
      set({
        activeGame: {
          ...activeGame,
          minimized: false
        }
      })
      fn?.(activeGame.code)
    }
  },
  closeActiveGame: (fn) => {
    const { activeGame } = get()
    if (!activeGame) {
      return
    }
    window.SW_LOBBY = {
      isGameMinimized: false
    }
    set({
      activeGame: null
    })
    fn?.(activeGame.code)
  },
  openActiveGame: async (gameCode, language, fn) => {
    const { activeGame, menuItems, dealerPictures, limits } = get()
    if (activeGame?.code === gameCode) {
      if (activeGame.minimized) {
        const newActiveGame = {
          ...activeGame,
          minimized: false
        }
        set({
          activeGame: newActiveGame
        })
        window.SW_LOBBY = {
          isGameMinimized: false
        }
        return fn?.(true)
      }
      return
    }
    const game = menuItems.flatMap(getMenuItemGames).find(({ code }) => code === gameCode)
    const info = await getPlayerApi(getPlayerToken()).getGameUrlInfo(gameCode, game)
    if (info?.newPlayerToken && info?.newPlayerToken !== getPlayerToken()) {
      setPlayerToken(info.newPlayerToken)
    }
    if (info?.url) {
      let title: string | undefined
      if (game?.title?.[language]) {
        title = game.title[language]
      }
      const translations = info.game?.features?.translations ?? {}
      if (info.game?.title) {
        translations.en = {
          title: info.game?.title
        }
      }
      if (translations[language]?.title) {
        title = translations[language].title
      }

      const newActiveGame = {
        ...(activeGame ?? {}),
        code: gameCode,
        url: info.url,
        title,
        tableId: game?.table?.tableId ?? info.game?.features?.live?.tableId,
        language,
        minimized: false
      }
      if (newActiveGame.url) {
        const { tableId, language } = newActiveGame
        const url = new URL(newActiveGame.url)
        url.searchParams.set('isLobby', '1')
        url.searchParams.set('modal', '1')
        if (language) {
          url.searchParams.set('lc', language)
          url.searchParams.set('language', language)
        }
        if (tableId) {
          const thumbnailUrl = dealerPictures.get(tableId)
          if (thumbnailUrl) {
            url.searchParams.set('thumbnailUrl', thumbnailUrl)
          }
          const limit = limits.find(item =>
            item.tableId === tableId &&
            item.gameCode === gameCode
          )
          if (limit) {
            url.searchParams.set('limit', limit.name)
          }
        } else {
          url.searchParams.set('fs', '0')
        }
        const isLive = getBooleanParam(url.searchParams.get('live'))

        newActiveGame.url = url.href
        newActiveGame.isLive = isLive
      }
      window.SW_LOBBY = {
        isGameMinimized: false
      }
      set({
        activeGame: newActiveGame
      })
      return fn?.(false)
    }
  },

  homeButton: null,
  setHomeButton: homeButton => set({
    homeButton
  }),

  lobbyOptions: null,
  liveManagerEndpoint: null,
  menuItems: [],
  menuItem: null,

  loadLobbyInfo: async (name?: string) => {
    const lobbyId = getLobbyId()
    if (get().menuItems.length === 0 && lobbyId) {
      const { options, menuItems, liveManagerEndpoint } = await getPlayerApi(getPlayerToken()).lobbyInfo(lobbyId)
      const tables = uniq(menuItems
        .flatMap(getMenuItemGames)
        .map(({ table, code }) => table ? { ...table, gameCode: code } : null)
        .filter((table): table is (LiveTableInfo & { gameCode: string }) => Boolean(table)))
      const favoriteSections = getSections(menuItems, ({ showFavoriteGames }) => showFavoriteGames)
      set({
        lobbyOptions: options,
        liveManagerEndpoint,
        menuItems,
        statuses: tables
          .map(({ tableId, isOnline }) => ({ tableId, open: isOnline ?? false }))
          .filter(({ open }) => open),
        scoreboards: tables
          .map(({ tableId, liveGameType: tableType, scoreboard }) => ({ tableId, tableType, scoreboard }))
          .filter((data): data is TableScoreboard => Boolean(data.scoreboard)),
        dealers: tables
          .map(({ tableId, dealer }) => ({ tableId, dealer }))
          .filter((data): data is TableDealerInfo => Boolean(data.dealer)),
        seatsInfos: tables
          .filter(({ liveGameType }) => liveGameType === GameType.BLACKJACK_SEVEN_SEAT)
          .map(({ tableId }) => ({
            tableId,
            tableType: GameType.BLACKJACK_SEVEN_SEAT,
            seatsInfo: {
              haveVacantSeat: true,
              countOccupiedSeats: 0,
              countPlayerSeats: 0,
              seats: {
                P1: BlackjackSeatType.Vacant,
                P2: BlackjackSeatType.Vacant,
                P3: BlackjackSeatType.Vacant,
                P4: BlackjackSeatType.Vacant,
                P5: BlackjackSeatType.Vacant,
                P6: BlackjackSeatType.Vacant,
                P7: BlackjackSeatType.Vacant
              }
            }
          })),
        numberOfPlayers: tables
          .map(({ tableId, numberOfPlayers }) => ({ tableId, numberOfPlayers }))
          .filter((data): data is TableNumberOfPlayers => data.numberOfPlayers !== undefined),
        limits: tables
          .map(({ tableId, limits, gameCode }) => ({
            tableId,
            gameCode,
            name: limits.find(({ isDefaultRoom }) => isDefaultRoom)?.name
          }))
          .filter((data): data is TableLimitRange => Boolean(data.name)),
        favoriteGames: favoriteSections.map(({ games }) => games).flat(),
        hasFavorite: favoriteSections.length > 0,
        recentGames: getSections(menuItems, ({ showRecentGames }) => showRecentGames).map(({ games }) => games).flat()
      })
    }
    set(({ menuItems }) => {
      const menuItem = name ? menuItems.find(({ slug }) => slug === name) : undefined
      return ({
        menuItem: menuItem ?? menuItems[0]
      })
    })
  },

  dealerPictures: new Map<string, string>(),
  refreshDealerPictures: (games) => {
    set(state => {
      const newDealerPictures = new Map(state.dealerPictures)
      for (const game of games) {
        if (game.table?.tableId) {
          const url = newDealerPictures.get(game.table.tableId) ?? game.table.dealer?.picture
          if (url) {
            newDealerPictures.set(game.table.tableId, updatePicture(url) ?? url)
          }
        }
      }
      return {
        dealerPictures: newDealerPictures
      }
    })
  },

  statuses: [],
  setStatus: (data) => set(state => ({
    statuses: [...state.statuses.filter(({ tableId }) => tableId !== data.tableId), data]
  })),

  dealers: [],
  setDealerInfo: (data) => set(state => ({
    dealers: [...state.dealers.filter(({ tableId }) => tableId !== data.tableId), data]
  })),

  scoreboards: [],
  setScoreboard: (data) => set(state => ({
    scoreboards: [...state.scoreboards.filter(({ tableId }) => tableId !== data.tableId), data]
  })),

  seatsInfos: [],
  setSeatsInfo: (id, tableType, seats) => set(state => ({
    seatsInfos: [
      ...state.seatsInfos.filter(({ tableId }) => tableId !== id),
      ...(Array.isArray(seats) ? [{ tableId: id, tableType, seatsInfo: toSeatsInfo(toTableSeats(seats, state.playerInfo?.code)) }] : [])
    ]
  })),
  updateSeatsInfo: ({ tableId, tableType, playerCode, seat, previousSeat }) => set(state => ({
    seatsInfos: state.seatsInfos.map((item) => ({
      ...item,
      ...(item.tableId === tableId ? {
        ...(tableType ? { tableType } : {}),
        seatsInfo: toSeatsInfo({
          ...item.seatsInfo.seats,
          ...(previousSeat ? { [previousSeat]: BlackjackSeatType.Vacant } : {}),
          ...(seat ? { [seat]: toSeatType(seat, [{ seat, playerCode }], state.playerInfo?.code) } : {})
        })
      } : {})
    }))
  })),
  toggleSeatsInfo: async (table, seat) => {
    set(state => ({
      seatsInfos: state.seatsInfos.map((item) => ({
        ...item,
        ...(item.tableId === table.tableId ? {
          seatsInfo: toSeatsInfo({
            ...item.seatsInfo.seats,
            ...(seat ? { [seat]: toSeatType(seat, [{ seat, playerCode: state.playerInfo?.code }], state.playerInfo?.code) } : {})
          })
        } : {})
      }))
    }))
    const { liveManagerEndpoint, playerInfo } = get()
    const liveApi = getLiveApi(liveManagerEndpoint, getPlayerToken())
    await liveApi?.toggleBlackjackSeat({
      provider: table.provider,
      tableId: table.tableId,
      nickname: playerInfo?.nickname,
      isVip: playerInfo?.isVip,
      seat
    })
  },

  numberOfPlayers: [],
  updateNumberOfPlayers: (data) => set(state => ({
    numberOfPlayers: [...state.numberOfPlayers.filter(({ tableId }) => tableId !== data.tableId), data]
  })),

  limits: [],
  updateLimitRange: (data) => set(state => ({
    limits: [...state.limits.filter(item =>
      !(item.tableId === data.tableId && item.gameCode === data.gameCode)
    ), data]
  })),

  hasFavorite: false,
  favoriteGames: [],
  toggleFavoriteGame: async (game) => {
    const isFavorite = get().favoriteGames.some(({ code }) => code === game.code)
    set(state => ({
      favoriteGames: [
        ...state.favoriteGames.filter(({ code }) => code !== game.code),
        ...(isFavorite ? [] : [game])
      ]
    }))
    const success = await getPlayerApi(getPlayerToken()).updateFavoriteGame(game.code, !isFavorite)
    if (!success) {
      set(state => ({
        favoriteGames: [
          ...state.favoriteGames.filter(({ code }) => code !== game.code),
          ...(isFavorite ? [game] : [])
        ]
      }))
    }
  },
  setFavoriteGame: (gameCode, isFavorite = false) => {
    const has = get().favoriteGames.some(({ code }) => code === gameCode)
    if (isFavorite && !has) {
      set(state => ({
        favoriteGames: uniqBy(state.favoriteGames.concat(state.menuItems
          .flatMap(getMenuItemGames)
          .filter(({ code }) => code === gameCode)), 'code')
      }))
    }
    if (!isFavorite && has) {
      set(state => ({
        favoriteGames: state.favoriteGames.filter(({ code }) => code !== gameCode)
      }))
    }
  },

  recentGames: [],
  setRecentGame: (gameCode, time) => set(state => {
    const game = state.menuItems.flatMap(getMenuItemGames).find(({ code }) => code === gameCode)
    if (!game) {
      return { recentGames: state.recentGames }
    }
    return {
      recentGames: uniqBy(state.recentGames.concat([{
        ...game,
        playedDate: `${time}`
      }]), 'code').sort((a, b) => {
        const bTime = b.playedDate ? new Date(b.playedDate).getTime() : 0
        const aTime = a.playedDate ? new Date(a.playedDate).getTime() : 0
        return bTime - aTime
      })
    }
  }),

  isNicknameModalOpen: false,
  setIsNicknameModalOpen: isNicknameModalOpen => set({
    isNicknameModalOpen: isNicknameModalOpen
  }),

  isOpenAsideMenu: false,
  setOpenAsideMenu: isOpenAsideMenu => set({
    isOpenAsideMenu
  }),

  isOpenLanguageModal: false,
  setOpenLanguageModal: setOpenLanguageModal => set({
    isOpenLanguageModal: setOpenLanguageModal
  }),

  limitsActiveTable: null,
  setLimitsActiveTable: limitsActiveTable => set({
    limitsActiveTable
  })
})), {
  name: 'sw-storage',
  partialize: state => ({ playerInfo: state.playerInfo })
}))
