import { throttle } from 'lodash-es'
import { useEffect, useRef } from 'react'
import { useErrorHandling } from '@/hooks/useErrorHandling.ts'
import { useTranslate } from '@/hooks/useTranslate.ts'
import { useStore } from '@/hooks/useStore.ts'
import { TokenIsMissing } from '@/models/error.ts'

const DEFAULT_INACTIVE_TIME = 600

export const useInactivityWatcher = () => {
  const timerRef = useRef<number | undefined>(undefined)
  const playerInactivityTime = useStore(state => Number(state.lobbyOptions?.playerInactivityTime ?? DEFAULT_INACTIVE_TIME))
  const { handleError } = useErrorHandling()
  const { t } = useTranslate()

  function clearTimer() {
    if (timerRef.current) {
      window.clearTimeout(timerRef.current)
      timerRef.current = undefined
    }
  }

  useEffect(() => {
    if (!playerInactivityTime) {
      console.info('[lobby] Inactivity timeout is disabled')
      clearTimer()
      return
    }
    console.info(`[lobby] Inactivity timeout set to ${playerInactivityTime} seconds`)

    const resetTimer = throttle(() => {
      clearTimer()
      timerRef.current = window.setTimeout(() => {
        handleError(new TokenIsMissing(t('EXPIRATION.message')))
      }, playerInactivityTime * 1000)
    }, 1000)

    resetTimer()

    const events = ['mousemove', 'mousedown', 'click', 'keydown', 'touchstart', 'touchmove', 'scroll']
    for (const event of events) {
      document.addEventListener(event, resetTimer)
    }
    return () => {
      clearTimer()
      for (const event of events) {
        document.removeEventListener(event, resetTimer)
      }
    }
  }, [handleError, t, playerInactivityTime])
}
