import { useTranslate } from '@/hooks/useTranslate.ts'
import { useMemo } from 'react'
import { useStore } from '@/hooks/useStore.ts'
import { getMenuItemGames } from '@/utils/game.ts'

interface Props {
  gameCode?: string
  title?: string
}

export const useGameTitle = ({ gameCode, title }: Props) => {
  const menuItems = useStore(state => state.menuItems)
  const { translate } = useTranslate()
  
  return useMemo(() => {
    if (title) {
      return title
    }
    return translate(menuItems.flatMap(getMenuItemGames).find(({ code }) => code === gameCode)?.title)
  }, [gameCode, menuItems, title, translate])
}
