import { useState, useRef, useEffect, useCallback } from "react";

interface UseDraggableProps {
  isMobile: boolean;
  isEnabled: boolean;
  isPortrait: boolean;
}

interface Position {
  x: number;
  y: number;
}

const positionInitialState = { x: 0, y: 0 };
const offsetInitialState = { x: 0, y: 0 };

const usePrevious = (value: boolean) => {
  const ref = useRef<boolean | undefined>(undefined);
  useEffect(() => {
    ref.current = value;
  });
  return ref.current;
};

export const useDraggable = ({ isMobile, isPortrait, isEnabled }: UseDraggableProps) => {
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState<Position>(positionInitialState);
  const offsetRef = useRef<Position>(offsetInitialState);
  const elementRef = useRef<HTMLDivElement | null>(null);
  const prevIsPortrait = usePrevious(isPortrait);
  const deltaY = isMobile ? 120 : 194;
  const deltaX = isMobile ? 30 : 44;
  
  const reset = () => {
    setPosition(positionInitialState);
    offsetRef.current = offsetInitialState
  }
  
  useEffect(() => {
    if (!isEnabled || !elementRef.current) {
      reset()
    }
  }, [isEnabled, elementRef]);
  
  useEffect(() => {
    if (elementRef.current && isPortrait !== prevIsPortrait) {
      reset()
    }
  }, [isPortrait, prevIsPortrait]);
  
  const handleStart = useCallback(
    (clientX: number, clientY: number) => {
      
      if (!elementRef.current) return;

      offsetRef.current = {
        x: clientX - position.x,
        y: clientY - position.y,
      };
      
      setIsDragging(true);
    },
    [position]
  );
  
  const handleMove = useCallback(
    (clientX: number, clientY: number) => {
      if (!isDragging) return;
      
      const element = elementRef.current;
      if (!element) return;
      const rect = element.getBoundingClientRect();
        const minX = -(window.innerWidth - rect.width - deltaX)
        const minY = -(window.innerHeight - rect.height - deltaY)
        const maxX = 0
        const maxY = 0
      
      
      let newX = clientX - offsetRef.current.x;
      let newY = clientY - offsetRef.current.y;
      
      
      newX = Math.max(Math.min(newX, maxX), minX);
      newY = Math.max(Math.min(newY, maxY), minY);
      
      setPosition({ x: newX, y: newY });
    },
    [deltaX, deltaY, isDragging]
  );
  
  const handleEnd = useCallback(() => {
    setIsDragging(false);
  }, []);
  
  useEffect(() => {
    if (!elementRef.current) return;
    elementRef.current.style.transform = `translate3d(${position.x}px, ${position.y}px, 0)`;
  }, [position]);

  useEffect(() => {
    if (!isEnabled) return;
    
    const handleMouseDown = (e: MouseEvent) => {
      e.stopPropagation()
      handleStart(e.clientX, e.clientY);
    };
    
    const handleTouchStart = (e: TouchEvent) => {
      e.stopPropagation()
      handleStart(e.touches[0].clientX, e.touches[0].clientY);
    };
    
    const handleMouseMove = (e: MouseEvent) => {
      e.stopPropagation()
      handleMove(e.clientX, e.clientY);
    };
    
    const handleTouchMove = (e: TouchEvent) => {
      e.stopPropagation()
      if (isDragging) {
        e.preventDefault();
        handleMove(e.touches[0].clientX, e.touches[0].clientY);
      }
    };
    
    const element = elementRef.current;
    if (!element) return;

    if (!isMobile) {
      element.addEventListener("mousedown", handleMouseDown);
      window.addEventListener("mousemove", handleMouseMove);
      window.addEventListener("mouseup", handleEnd);
    } else {
      element.addEventListener("touchstart", handleTouchStart);
      window.addEventListener("touchmove", handleTouchMove, { passive: false });
      window.addEventListener("touchend", handleEnd);
    }

    return () => {
      if (!isMobile) {
        element.removeEventListener("mousedown", handleMouseDown);
        window.removeEventListener("mousemove", handleMouseMove);
        window.removeEventListener("mouseup", handleEnd);
      } else {
        element.removeEventListener("touchstart", handleTouchStart);
        window.removeEventListener("touchmove", handleTouchMove);
        window.removeEventListener("touchend", handleEnd);
      }
    };
  }, [isEnabled, isMobile, handleStart, handleMove, handleEnd, isPortrait, isDragging]);
  
  return {
    ref: elementRef,
    isDragging
  };
};
