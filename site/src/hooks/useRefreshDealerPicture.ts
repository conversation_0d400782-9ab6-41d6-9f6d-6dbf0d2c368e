import { useEffect, useRef, useState } from 'react'
import { useStore } from '@/hooks/useStore.ts'
import type { GameLiveInfo } from '@/models/game.ts'

const INTERVAL = 15 * 1000

export function useRefreshDealerPicture(games: GameLiveInfo[]) {
  const refreshRef = useRef<number | undefined>(undefined)
  const [hidden, setHidden] = useState<boolean>(document.hidden)
  const refreshIntervalLive = useStore(state => state.lobbyOptions?.refreshIntervalLive)
  const refreshDealerPictures = useStore(state => state.refreshDealerPictures)

  useEffect(() => {
    const onVisibilityChange = () => {
      setHidden(document.hidden)
    }
    document.addEventListener('visibilitychange', onVisibilityChange, false)
    return () => {
      document.removeEventListener('visibilitychange', onVisibilityChange)
    }
  }, [])

  useEffect(() => {
    if (refreshRef.current) {
      window.clearInterval(refreshRef.current)
    }
    if (!hidden) {
      refreshRef.current = window.setInterval(() => {
        refreshDealerPictures(games)
      }, refreshIntervalLive ?? INTERVAL)

      return () => {
        window.clearInterval(refreshRef.current)
      }
    }
  }, [games, hidden, refreshIntervalLive, refreshDealerPictures])
}
