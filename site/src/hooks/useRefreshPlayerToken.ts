import { useEffect, useRef } from 'react'
import { decodeToken } from '@/utils/token.ts'
import { getPlayerApi } from '@/services/playerApi.ts'
import { useErrorHandling } from '@/hooks/useErrorHandling.ts'
import { getPlayerToken, setPlayerToken } from '@/utils/playerInfo.ts'

const FEW_MINUTES = 5 * 60 * 1000
const MAX_TIMEOUT_VALUE = 2147483647

export const useRefreshPlayerToken = () => {
  const intervalRef = useRef<number | undefined>(undefined)
  const { handleError } = useErrorHandling()

  useEffect(() => {
    const playerToken = getPlayerToken()
    if (playerToken) {
      const payload = decodeToken<{ exp: number }>(playerToken)
      if (payload?.exp) {
        const expirationDate = new Date(0)
        expirationDate.setUTCSeconds(payload.exp)
        const refreshTimeout = Math.min(expirationDate.getTime() - Date.now() - FEW_MINUTES, MAX_TIMEOUT_VALUE)
        console.info(`[lobby] Set refresh token at ${new Date(Date.now() + refreshTimeout).toLocaleTimeString()}`)

        intervalRef.current = window.setTimeout(() => {
          getPlayerApi(getPlayerToken()).refresh()
            .then(data => {
              if (data?.token) {
                setPlayerToken(data.token)
              }
            })
            .catch(handleError)
        }, refreshTimeout)
      }
    }
    return () => {
      if (intervalRef.current) {
        window.clearTimeout(intervalRef.current)
      }
    }
  }, [handleError])
}
