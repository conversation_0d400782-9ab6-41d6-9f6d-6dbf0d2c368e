import { decodeToken } from './token'
import storage from './storage'
import type { PlayerTokenPayload } from '../models/playerInfo'

const DEFAULT_CURRENCY = 'usd'
const NAME_PLAYER_TOKEN = 'playerToken'
const NAME_PLAYER_CURRENCY = 'playerCurrency'

export function getPlayerCurrency() {
  return storage.getItem(NAME_PLAYER_CURRENCY) ?? DEFAULT_CURRENCY
}

export function setPlayerCurrency(currency: string) {
  return storage.setItem(NAME_PLAYER_CURRENCY, currency)
}

export function getPlayerToken() {
  return storage.getItem(NAME_PLAYER_TOKEN)
}

export function setPlayerToken(token: string | null) {
  if (token) {
    storage.setItem(NAME_PLAYER_TOKEN, token)
    const payload = decodeToken<PlayerTokenPayload>(token)

    if (payload?.currency) {
      storage.setItem(NAME_PLAYER_CURRENCY, payload?.currency)
    } else {
      storage.removeItem(NAME_PLAYER_CURRENCY)
    }
  } else {
    storage.removeItem(NAME_PLAYER_TOKEN)
    storage.removeItem(NAME_PLAYER_CURRENCY)
  }
}
