export function matchPaths(routePaths: string[], currentPath: string): [boolean, Record<string, string>] {
  for (const routePath of routePaths) {
    const [isMatch, params] = matchPath(routePath, currentPath)
    if (isMatch) {
      return [isMatch, params]
    }
  }
  return [false, {}]
}

export function matchPath(routePath: string, currentPath: string): [boolean, Record<string, string>] {
  const params: Record<string, string> = {}
  if (routePath === currentPath) {
    return [true, params]
  }
  if (routePath.endsWith('*')) {
    const baseRoutePath = routePath.slice(0, -1)
    return [
      currentPath.startsWith(baseRoutePath),
      { wildcard: currentPath.slice(baseRoutePath.length) }
    ]
  }
  const routeSegments = routePath.split('/').filter(Boolean)
  const currentSegments = currentPath.split('/').filter(Boolean)
  if (routeSegments.length !== currentSegments.length) {
    return [false, params]
  }
  const isMatch = routeSegments.every((segment, index) => {
    if (segment.startsWith(':')) {
      const paramName = segment.slice(1)
      params[paramName] = currentSegments[index]
      return true
    }
    if (segment === '*') {
      return true
    }
    return segment === currentSegments[index]
  })
  return [isMatch, params]
}

export function getBooleanParam(value?: string | null) {
  if (value === undefined || value === null) {
    return false
  }
  return ['yes', '1', 'true'].includes(value.toLowerCase())
}
