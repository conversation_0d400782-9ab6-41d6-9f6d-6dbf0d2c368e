export interface ValidationError {
  type: string
  message: string
  params?: Record<string, string>
}

export type ValidatorFn = (value: string) => ValidationError | null

export class StringValidators {
  static Required = (message = 'PASSWORD_CHANGE.required'): ValidatorFn => (value: string) => {
    if (!value) {
      return {
        type: 'required',
        message
      }
    }
    return null
  }

  static MinLength = (minLength = 8, errorMessage = 'PASSWORD_CHANGE.minLength'): ValidatorFn => (value: string) => {
    if (value.length < minLength) {
      return {
        type: 'minLength',
        message: errorMessage,
        params: { value: minLength.toString() }
      }
    }
    return null
  }

  static ContainsDigit = (min = 1, errorMessage = 'PASSWORD_CHANGE.containDigit'): ValidatorFn => (value: string) => {
    if (value.replace(/\D+/g, '').length < min) {
      return {
        type: 'containDigit',
        message: errorMessage,
        params: { value: min.toString() }
      }
    }
    return null
  }

  static ContainsLowercase = (min = 1, errorMessage = 'PASSWORD_CHANGE.containLowercase'): ValidatorFn => (value: string) => {
    if (value.replace(/[^a-z]/g, '').length < min) {
      return {
        type: 'containLowercase',
        message: errorMessage,
        params: { value: min.toString() }
      }
    }
    return null
  }

  static ContainsUppercase = (min = 1, errorMessage = 'PASSWORD_CHANGE.containUppercase'): ValidatorFn => (value: string) => {
    if (value.replace(/[^A-Z]/g, '').length < min) {
      return {
        type: 'containUppercase',
        message: errorMessage,
        params: { value: min.toString() }
      }
    }
    return null
  }

  static Equal = (currentPassword: string, errorMessage = 'PASSWORD_CHANGE.equal'): ValidatorFn => (value: string) => {
    if (currentPassword && value && currentPassword === value) {
      return {
        type: 'equal',
        message: errorMessage
      }
    }
    return null
  }

  static NotEqual = (newPassword: string, errorMessage = 'PASSWORD_CHANGE.notMatch'): ValidatorFn => (value: string) => {
    if (newPassword && value && newPassword !== value) {
      return {
        type: 'notMatch',
        message: errorMessage
      }
    }
    return null
  }
}

export function getError(errors: ValidationError[], errorType: string): ValidationError | undefined {
  return errors.find(error => error.type === errorType)
}
