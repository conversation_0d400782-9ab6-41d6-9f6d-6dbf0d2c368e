import type { AppConfig, TokenPayload } from '../models/appConfig'
import { metaTag } from './html'
import { decodeToken } from './token'
import storage from './storage.ts'

const NAME = 'terminalToken'

export function getAppConfig(): AppConfig {
  const searchParams = new URLSearchParams(document.location.search)
  const token = searchParams.get(NAME) ?? storage.getItem(NAME) ?? metaTag(NAME)
  const payload = decodeToken<TokenPayload>(token)
  return {
    token,
    lobbyId: payload?.lobbyId,
    playerSocketUrl: import.meta.env.VITE_PLAYER_SOCKET_API
  }
}
