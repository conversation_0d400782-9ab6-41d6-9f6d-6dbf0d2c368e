function parse(title: string): Record<string, string> {
  const parts = title.split('|')
  if (parts.length < 2) {
    return {}
  }
  return parts.reduce((result, part, index) => {
    let [lang, template] = part.split(':', 2)
    if (template === undefined) {
      if (index === 0) {
        template = lang
        lang = 'en'
      }
      if (index === 1) {
        template = lang
        lang = 'zh-cn'
      }
    }
    if (template === undefined) {
      return result
    }
    return {
      ...result,
      [lang]: template
    }
  }, {})
}

export function buildCashierUrl(url: string, { ticket, language }: { ticket?: string, language?: string }) {
  let newUrl = url
  if (ticket && newUrl.includes('{{ticket}}')) {
    newUrl = newUrl.replace(/{{ticket}}/i, ticket)
  }
  if (language && newUrl.includes('{{language:')) {
    const [prefix, data] = newUrl.split('{{language:', 2)
    const [body, suffix] = data.split('}}', 2)
    const translations = parse(body)
    if (language in translations) {
      newUrl = `${prefix}${translations[language]}${suffix}`
    }
  }
  return newUrl
}
