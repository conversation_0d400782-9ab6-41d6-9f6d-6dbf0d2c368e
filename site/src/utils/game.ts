import type { GameLiveInfo, SeatsInfo, TableSeats } from '../models/game.ts'
import { BlackjackSeatType } from '../models/game.ts'
import type { Blackjack } from 'sw-live-core'
import type { LiveTableStatus } from '@/hooks/useStore.ts'
import type { MenuItem } from '@/models/lobby.ts'
import { uniq } from 'lodash-es'

export function getMenuItemGames(menuItem: MenuItem) {
  return uniq([
    ...(menuItem.fullPage?.game.table ? [menuItem.fullPage.game] : []),
    ...(menuItem.sections ? menuItem.sections.map(({ games }) => games).flat() : [])
  ])
}

export function toSeatType(seat: Blackjack.Seat, seatStates: Blackjack.SeatState[], playerCode?: string | null): BlackjackSeatType {
  const seatState = seatStates.find((seatState) => seatState.seat === seat)
  if (playerCode && seatState?.playerCode === playerCode) {
    return BlackjackSeatType.Player
  }
  return seatState?.playerCode ? BlackjackSeatType.Occupied : BlackjackSeatType.Vacant
}

export function toTableSeats(seats: Blackjack.SeatState[], playerCode?: string | null): TableSeats {
  return {
    P1: toSeatType('P1', seats, playerCode),
    P2: toSeatType('P2', seats, playerCode),
    P3: toSeatType('P3', seats, playerCode),
    P4: toSeatType('P4', seats, playerCode),
    P5: toSeatType('P5', seats, playerCode),
    P6: toSeatType('P6', seats, playerCode),
    P7: toSeatType('P7', seats, playerCode)
  }
}

export function toSeatsInfo(seats: TableSeats): SeatsInfo {
  let haveVacantSeat = false
  let countOccupiedSeats = 0
  let countPlayerSeats = 0

  const keys = Object.keys(seats)

  for (const key of keys) {
    if (seats[key as keyof TableSeats] === BlackjackSeatType.Player) {
      countPlayerSeats++
      haveVacantSeat = true
    }

    if (seats[key as keyof TableSeats] === BlackjackSeatType.Occupied) {
      countOccupiedSeats++
    }
    if (seats[key as keyof TableSeats] === BlackjackSeatType.Vacant) {
      haveVacantSeat = true
    }
  }

  return {
    haveVacantSeat,
    countOccupiedSeats,
    countPlayerSeats,
    seats
  }
}

export const isGameOpen = (statuses: LiveTableStatus[]) => (game: GameLiveInfo): boolean => {
  if (game.table?.tableId === undefined) {
    return true
  }
  return statuses.find(({ tableId }) => tableId === game.table?.tableId)?.open === true
}
