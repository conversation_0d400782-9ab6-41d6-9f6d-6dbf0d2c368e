interface StorageType {
  getItem( key: string ): string | null;

  removeItem( key: string ): void;

  setItem( key: string, value: string ): void;
}

const noop: StorageType = {
  getItem(): null {
    return null;
  },

  removeItem(): void {
    // no-op
  },

  setItem(): void {
    // no-op
  }
};

function inPrivateMode(): boolean {
  try {
    localStorage.test = 2;
    localStorage.removeItem('test');
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (e) {
    return true;
  }
  return false;
}

const storage: StorageType = inPrivateMode() ? noop : window.localStorage;

export default storage;
