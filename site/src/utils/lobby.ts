import type { GameLiveInfo, GameLiveInfoLimit, LiveTableId, LiveTableInfo } from '../models/game.ts'
import slugify from 'slugify'
import type { LiveManagerEndpoint, MenuItem, SectionItem } from '../models/lobby.ts'
import type { DealerInfo, GameType } from 'sw-live-core'
import { TableStatus } from 'sw-live-core'
import type { FullPageGameType } from 'sw-fullpage'
import { omit, uniq } from 'lodash-es'
import { getMenuItemGames } from '@/utils/game.ts'

enum GameModuleType {
  sw_live_erol_atom = 'sw_live_erol_atom'
}

interface LiveTable {
  provider: string
  tableId: string
}

interface GameFeatureLive extends LiveTable {
  gameType: GameType
  dealer?: DealerInfo
  status?: TableStatus
  language?: string
  tables?: LiveTable[]
  tableTime?: string
  customGameModule?: GameModuleType
  customPicture?: string
}

interface GameLiveLimits {
  winMax?: number
  stakeMax?: number
  stakeMin?: number
  isDefaultRoom?: true
  order?: number
  name: string
}

export type GameTranslations = Record<string, {
  title: string
  description?: string
}>

interface Game {
  code: string
  type?: string
  isLive?: boolean
  isTest?: boolean
  gameModule?: GameModuleType
  title?: string
  isFavorite?: boolean
  playedDate?: string
  providerTitle?: string
  defaultInfo?: {
    images?: {
      poster?: string
      screenshots?: string[]
      screenshots_hd?: string[]
      overlay?: string
    }
    screenshots?: string[]
    screenshots_hd?: string[]
  }
  features?: {
    isComission?: boolean
    live?: GameFeatureLive & {
      limits?: GameLiveLimits[]
      providerSettings?: {
        multiSeatLimit?: number
      }
    }
    translations?: GameTranslations
  }
}

interface GameInfo extends Game {
  overlayUrl?: Record<string, string>
  ribbon?: Record<string, LobbyMenuItemRibbon>
}

interface LobbyMenuItemRibbon {
  text: string
  bg: string
  color: string
}

export interface ExternalWidget {
  src: string
  tag: string
  path: string
  options?: unknown
  games?: Game[]
}

interface LobbyMenuItemInfo {
  title: string
  icon?: string
  ribbon?: LobbyMenuItemRibbon
}

interface LobbySubMenuItemOptions {
  isCommissionFilter?: boolean
  isGridLayout?: boolean
  widgets?: Record<string, ExternalWidget>
}

interface LobbySubMenuItem extends LobbyMenuItemInfo {
  id: string
  widget?: ExternalWidget
  gameCategoryId?: string
  showFavoriteGames?: boolean
  showRecentGames?: boolean
  translations?: Record<string, LobbyMenuItemInfo>
  options?: LobbySubMenuItemOptions
  gameRibbons?: Record<string, Record<string, LobbyMenuItemRibbon>>
  overlayUrls?: Record<string, Record<string, string>>
  games: Game[]
}

interface LobbyMenuItem extends LobbySubMenuItem {
  slug?: string
  subcategories?: LobbySubMenuItem[]
}

export interface LobbyData {
  title?: string
  info?: {
    liveManagerUrl?: LiveManagerEndpoint
    menuItems?: LobbyMenuItem[]
    options?: {
      key: string
      value: unknown
    }[]
  }
}

export interface GameUrlInfo {
  url?: string
  newPlayerToken?: string
  historyUrl?: string
}

export interface ExtendedGameUrlInfo extends GameUrlInfo {
  game?: {
    title: Game['title'],
    features: {
      translations: GameTranslations,
      live: {
        tableId: LiveTable['tableId']
      }
    }
  }
}

export function buildMenuItems(menuItems: LobbyMenuItem[] = []) {
  return menuItems.map<MenuItem>(toMenuItem)
}

function toFullPage({ widget, ...menuItem }: LobbyMenuItem): Pick<MenuItem, 'fullPage'> {
  if (widget?.tag === 'x-widget-fullpage') {
    const { gameType } = (widget.options ?? {}) as { gameType?: FullPageGameType }
    const games = toGameLiveInfos(widget.games ?? [], menuItem)
    const game = games.length ? games[0] : undefined
    if (gameType && game) {
      return {
        fullPage: {
          game,
          gameType
        }
      }
    }
  }
  return {}
}

function toMenuItem(menuItem: LobbyMenuItem, index: number): MenuItem {
  const { slug, title, icon, ribbon, translations, subcategories, options } = menuItem

  const sections: LobbySubMenuItem[] = subcategories?.length ? subcategories : [omit(menuItem, ['subcategories', 'slug'])]

  const item: MenuItem = {
    slug: slug?.length ? slug : `${slugify(title, { replacement: '_', lower: true })}_${index}`,
    title: { en: title },
    ...(icon ? { icon: { en: icon } } : {}),
    ...(ribbon ? { ribbon: { en: ribbon } } : {}),
    sections: sections.map(toSectionItem),
    ...toFullPage(menuItem),
    isGridLayout: Boolean(options?.isGridLayout)
  }
  for (const [lang, value] of Object.entries(translations ?? {})) {
    if (value.title) {
      item.title[lang] = value.title
    }
    if (value.icon) {
      item.icon = {
        ...(item.icon ?? {}),
        [lang]: value.icon
      }
    }
    if (value.ribbon) {
      item.ribbon = {
        ...(item.ribbon ?? {}),
        [lang]: value.ribbon
      }
    }
  }

  item.tableIds = uniq(getMenuItemGames(item).map(({ table }) => table)
    .filter((table): table is LiveTableInfo => Boolean(table))
    .map<LiveTableId>(({ provider, tableId }) => ({ provider, tableId })))

  return item
}

function toSectionItem({ title, translations, games, ...menuItem }: LobbySubMenuItem, index: number): SectionItem {
  const item: SectionItem = {
    id: `${slugify(title, { replacement: '_', lower: true })}_${index}`,
    title: { en: title },
    games: toGameLiveInfos(games ?? [], menuItem),
    showFavoriteGames: menuItem.showFavoriteGames ?? false,
    showRecentGames: menuItem.showRecentGames ?? false
  }
  for (const [lang, value] of Object.entries(translations ?? {})) {
    if (value.title) {
      item.title[lang] = value.title
    }
  }
  return item
}

function getGameLiveInfoLimits(game: Game): GameLiveInfoLimit[] {
  const limits = game.features?.live?.limits ?? []
  return limits.map(limit => ({
    name: limit.name,
    stakeMin: limit.stakeMin ?? 0,
    stakeMax: limit.stakeMax ?? 0,
    isDefaultRoom: limit.isDefaultRoom ?? limits.length === 1
  })) ?? []
}

function toGameLiveInfos(games: Game[], item: Pick<LobbySubMenuItem, 'overlayUrls' | 'gameRibbons'>): GameLiveInfo[] {
  const gameOverlayUrls: LobbySubMenuItem['overlayUrls'] = {}
  for (const [lang, value] of Object.entries(item.overlayUrls ?? {})) {
    for (const [gameCode, url] of Object.entries(value)) {
      gameOverlayUrls[gameCode] = {
        ...gameOverlayUrls[gameCode],
        [lang]: url
      }
    }
  }
  const gameRibbons: LobbySubMenuItem['gameRibbons'] = {}
  for (const [lang, value] of Object.entries(item.gameRibbons ?? {})) {
    for (const [gameCode, ribbon] of Object.entries(value)) {
      gameRibbons[gameCode] = {
        ...gameRibbons[gameCode],
        [lang]: ribbon
      }
    }
  }
  return games.map(game => toGameLiveInfo({
    ...game,
    ribbon: gameRibbons[game.code],
    overlayUrl: gameOverlayUrls[game.code] ?? (game.defaultInfo?.images?.overlay ? { en: game.defaultInfo?.images?.overlay } : undefined)
  }))
}

function toGameLiveInfo(game: GameInfo): GameLiveInfo {
  let table: LiveTableInfo | undefined
  const live = game.features?.live
  if (live) {
    table = {
      tableId: live.tableId,
      provider: live.provider,
      liveGameType: live.gameType,
      isOnline: game.features?.live?.status === TableStatus.ONLINE,
      multiSeatLimit: live.providerSettings?.multiSeatLimit,
      isAtom: live.customGameModule === GameModuleType.sw_live_erol_atom,
      dealer: {
        ...(live.dealer?.name ? { name: live.dealer.name } : {}),
        ...(live.dealer?.picture ? { picture: live.dealer.picture } : {})
      },
      limits: getGameLiveInfoLimits(game)
    }
  }

  const title: GameLiveInfo['title'] = game.title ? { en: game.title } : {}
  if (game.features?.translations) {
    for (const [lang, value] of Object.entries(game.features?.translations)) {
      if (value.title) {
        title[lang] = value.title
      }
    }
  }

  return {
    code: game.code,
    ...(Object.keys(title) ? { title } : {}),
    ...(table ? { table } : {}),
    isLiveGame: Boolean(table),
    isOnline: table ? (game.features?.live?.status === TableStatus.ONLINE) : true,
    language: game.features?.live?.language,
    ...(game.overlayUrl ? { overlayUrl: game.overlayUrl } : {}),
    ...(game.ribbon ? { ribbon: game.ribbon } : {}),
    ...(game.defaultInfo?.images?.poster ? { poster: game.defaultInfo?.images?.poster } : {})
  }
}
