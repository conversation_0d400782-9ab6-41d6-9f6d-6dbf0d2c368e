import { createRoot } from 'react-dom/client'
import { Login } from './components/Login/Login.tsx'
import { DeviceProvider } from '@/components/DeviceStore'
import { TranslateStore } from '@/components/TranslateStore'
import { LobbyOptionsProvider } from '@/components/LobbyOptionsProvider.tsx'
import { ErrorBoundary } from '@/components/ErrorBoundary/ErrorBoundary.tsx'
import '../styles.scss'

createRoot(document.getElementById('root')!).render(
  <DeviceProvider>
    <TranslateStore>
      <ErrorBoundary>
        <LobbyOptionsProvider>
          <Login />
        </LobbyOptionsProvider>
      </ErrorBoundary>
    </TranslateStore>
  </DeviceProvider>
)
