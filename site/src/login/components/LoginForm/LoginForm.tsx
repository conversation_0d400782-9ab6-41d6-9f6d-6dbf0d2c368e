import { useTranslate } from '@/hooks/useTranslate'
import type { ErrorData } from '@/models/error'
import type { CaptchaData } from '@/services/terminalApi'
import type { FormEvent } from 'react'
import { useState } from 'react'
import { Captcha } from '../Captcha/Captcha'
import { LoginWrapper } from '../LoginWrapper/LoginWrapper'
import { PasswordInput } from '../PasswordInput/PasswordInput'
import { RememberLogin } from '../RememberLogin/RememberLogin'
import { ServerError } from '../ServerError/ServerError'
import './LoginForm.scss'

interface Props {
  waiting: boolean
  captcha?: CaptchaData
  serverError?: ErrorData
  setCaptcha: (captcha?: CaptchaData) => void
  setCaptchaToken: (token: string) => void
  onSubmit: (code: string, password: string, force?: boolean) => void
}

export const LoginForm = ({
  waiting,
  captcha,
  setCaptcha,
  setCaptchaToken,
  serverError,
  onSubmit
}: Props) => {
  const [playerCode, setPlayerCode] = useState<string>(localStorage.getItem('LOGIN_REMEMBERED') ?? '')
  const [password, setPassword] = useState<string>('')
  const { t } = useTranslate()

  const handleSubmit = (event?: FormEvent): void => {
    if (event) {
      event.preventDefault()
    }
    if (!playerCode || !password) {
      return
    }
    onSubmit(playerCode, password)
  }

  return (
    <LoginWrapper>
      <form className={`login-form ${waiting ? 'blur' : ''}`} onSubmit={handleSubmit}>
        <ServerError error={serverError} />

        <div className="login-form__control control">
          <input
            type="text"
            autoComplete="username"
            className="input bo-input"
            name="playerCode"
            value={playerCode}
            onChange={(e) => setPlayerCode(e.target.value)}
            placeholder={t('LOGIN.playerCodePlaceholder')}
          />
        </div>

        <PasswordInput className="login-form__control" password={password} setPassword={setPassword} />
        <RememberLogin className="login-form__control login-form__control--checkbox" playerCode={playerCode} />

        <Captcha
          captcha={captcha}
          setCaptchaToken={setCaptchaToken}
          setCaptcha={setCaptcha}
          playerCode={playerCode}
        />

        <button className="login-form__submit btn btn--highlight btn-login bo-btn-highlight">
          {t('LOGIN.loginButtonTitle')}
        </button>
      </form>
    </LoginWrapper>
  )
}
