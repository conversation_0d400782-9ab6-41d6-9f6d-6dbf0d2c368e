@use '../../../styles/variables';

.login-form {
    position: relative;
    width: 320px;
    height: auto;
    padding: 35px 35px 20px;
    margin: auto;
    overflow: auto;

    &.blur {
      filter: blur(2px);
    }

    &__control {
      position: relative;
      margin-bottom: 18px;
      &--checkbox {
        label {
          font-size: 13px;
        }
      }
    }

    &__message {
      margin: 0 0 35px;
      color: rgba(255, 255, 255, 0.7);
      font-size: 14px;
      text-transform: uppercase;
    }

    &__submit {
      display: block;
      margin:
       25px auto 0;
    }
  }

  .control-invalid {
    border-color: variables.$color-error;
  }

  .input {
    display: block;
    height: 24px;
    width: 100%;
    padding: 0 5px;
    font-size: 13px;
    color: #fff;
    border: none;
    box-shadow: none;
    background-color: transparent;
    border-bottom: 1px solid #363636;
    &::-webkit-input-placeholder { /* Edge */
      color: variables.$color-default;
    }
  
    &:-ms-input-placeholder { /* Internet Explorer 10-11 */
      color: variables.$color-default;
    }
  
    &::placeholder {
      color: variables.$color-default;
    }
  }
  
 