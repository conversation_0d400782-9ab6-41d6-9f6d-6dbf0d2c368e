import type { MouseEvent } from 'react'
import { useEffect } from 'react'
import { useTranslate } from '@/hooks/useTranslate'
import './SecondSession.scss'

interface Props {
  onClose: () => void
}

export const SecondSession = ({ onClose }: Props) => {
  const { t } = useTranslate()

  useEffect(() => {
    const timer = window.setTimeout(() => {
      onClose()
    }, 3000)

    return () => {
      window.clearTimeout(timer)
    }
  }, [onClose])

  const handleClose = (event: MouseEvent) => {
    event.preventDefault()
    onClose()
  }

  return (
    <div className="expiration-modal">
      <div className="expiration-modal__dialog bo-modal-bg">
        <div className="expiration-modal__body">
          <span className="expiration-modal__message">
            {t('SERVER_ERRORS.LOGIN.playerSecondSession')}
          </span>
          <span className="expiration-modal__message expiration-modal__message--login">
            {t('EXPIRATION.pleaseLogin')}
          </span>
        </div>
        <div className="expiration-modal__footer">
          <div
            className="btn btn--highlight bo-highlight-bg"
            style={{ margin: '0 auto' }}
            onClick={handleClose}
          >
            <span className="btn-inner">{t('SETTINGS.ok')}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
