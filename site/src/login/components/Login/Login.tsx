import { useState } from 'react'
import { ChangePassword } from '../ChangePassword/ChangePassword'
import { LoginForm } from '../LoginForm/LoginForm'
import type { CaptchaData, LoginPlayerInfo } from '@/services/terminalApi'
import { getTerminalApi } from '@/services/terminalApi'
import type { ErrorData } from '@/models/error'
import { SecondSession } from '../SecondSession/SecondSession'
import { useLobbyOptions } from '@/hooks/useLobbyOptions'
import { setPlayerToken } from '@/utils/playerInfo'

export const Login = () => {
  const [loginInfo, setLoginInfo] = useState<LoginPlayerInfo | null>(null)
  const [secondSession, setSecondSession] = useState<{ code: string, password: string } | null>(null)
  const [waiting, setWaiting] = useState<boolean>(false)
  const [captcha, setCaptcha] = useState<CaptchaData>()
  const [captchaToken, setCaptchaToken] = useState<string>('')
  const [serverError, setServerError] = useState<ErrorData>()
  const { disableLogout } = useLobbyOptions()

  const onSubmit = (code: string, password: string, force = false): void => {
    setWaiting(true)
    getTerminalApi().login({
      code: code.trim(),
      password: password.trim(),
      force,
      ...(captcha?.csrfToken && captchaToken.trim() ? {
        captchaToken: captchaToken.trim(),
        csrfToken: captcha.csrfToken
      } : {})
    })
      .then(info => {
        if (info) {
          if (info.isPasswordTemp) {
            setLoginInfo(info)
          } else {
            setPlayerToken(info?.token ?? null)
            window.location.href = '/'
          }
        }
        setServerError(undefined)
      })
      .catch((error: ErrorData) => {
        setCaptcha(error?.extraData?.csrfToken ? {
          csrfToken: error?.extraData?.csrfToken,
          image: error?.extraData?.image ?? ''
        } : undefined)

        if (error?.code === 860) {
          setSecondSession({ code, password })
        } else {
          setServerError(error)
        }
      })
      .finally(() => {
        setWaiting(false)
      })
  }

  const handleClose = () => {
    if (secondSession) {
      const { code, password } = secondSession
      onSubmit(code, password, true)
    }
    setSecondSession(null)
  }

  if (disableLogout) {
    return null
  }
  
  if (secondSession) {
    return <SecondSession onClose={handleClose} />
  }

  if (loginInfo?.isPasswordTemp) {
    return <ChangePassword loginInfo={loginInfo} onSuccess={handleClose} />
  }

  return (
    <LoginForm
      waiting={waiting}
      captcha={captcha}
      setCaptcha={setCaptcha}
      setCaptchaToken={setCaptchaToken}
      serverError={serverError}
      onSubmit={onSubmit}
    />
  )
}
