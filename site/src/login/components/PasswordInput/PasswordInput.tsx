import { useState, useRef } from 'react'
import { useTranslate } from '@/hooks/useTranslate'
import './PasswordInput.scss'

interface Props {
  password: string
  setPassword: (value: string) => void
  className?: string
}

export const PasswordInput = ({ password, setPassword, className }: Props) => {
  const [passwordType, setPasswordType] = useState<'password' | 'text'>('password')
  const passwordRef = useRef<HTMLInputElement>(null)
  const { t } = useTranslate()

  const onSeeClick = () => {
    setPasswordType(prevType => prevType === 'password' ? 'text' : 'password')
    passwordRef.current?.focus()
  }

  return (
    <div className={`control ${className}`}>
      <input
        ref={passwordRef}
        type={passwordType}
        className="input bo-input"
        name="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        autoComplete="false"
        placeholder={t('LOGIN.passwordPlaceholder')}
      />
      <div className={`login-form-eye ${passwordType === 'text' ? 'visible' : ''}`} onClick={onSeeClick}>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox='0 0 61 32'>
          <path d="M30.684 32C14.821 32 0 17.824 0 15.998 0 14.114 14.728 0 30.684 0h.5c15.719.307 30.137 14.056 30.184 16.002C61.368 17.779 47.017 32 30.684 32zM3.578 16.048A53.187 53.187 0 0 0 18.31 26.224l.328.137a15.873 15.873 0 0 1-3.86-10.405c0-3.917 1.411-7.504 3.753-10.281l-.02.024C12.69 8.261 7.732 11.751 3.59 16.036l-.013.013zM30.73 2.823c-7.251 0-13.13 5.878-13.13 13.13s5.878 13.13 13.13 13.13c7.25 0 13.128-5.877 13.13-13.126v-.003c0-7.251-5.878-13.13-13.13-13.13zm12.227 2.869a15.87 15.87 0 0 1 3.738 10.263 15.89 15.89 0 0 1-3.972 10.534l.015-.018c5.859-2.617 10.855-6.13 15.057-10.424l.008-.009a43.049 43.049 0 0 0-14.568-10.24l-.28-.106zM30.73 22.589a6.542 6.542 0 0 1 0-13.084 8.205 8.205 0 0 1 1.858.264l-.057-.013a3.22 3.22 0 0 0-.918 2.682l-.002-.016a2.936 2.936 0 0 0 2.439 2.351l.016.002a3.317 3.317 0 0 0 2.63-1.308l.006-.008c.312.762.515 1.646.564 2.569l.001.02a6.542 6.542 0 0 1-6.535 6.539z"/>
        </svg>
      </div>
    </div>
  )
}
