import { useTranslate } from '@/hooks/useTranslate'
import type { ErrorData } from '@/models/error'
import './ServerError.scss'

interface Props {
  error?: ErrorData | null
}

export const ServerError = ({ error }: Props) => {
  const { t } = useTranslate()

  if (!error) {
    return null
  }

  const getErrorMessage = () => {
    switch (error.code) {
      case 40:
        return t('SERVER_ERRORS.LOGIN.invalidLogin')
      case 43:
      case 102:
      case 201:
      case 701:
        return t('SERVER_ERRORS.LOGIN.countryRestricted')
      case 230:
        return t('SERVER_ERRORS.LOGIN.loginBlocked')
      case 712:
        return t('SERVER_ERRORS.LOGIN.playerSuspended')
      case 813:
        return t('SERVER_ERRORS.LOGIN.invalidCaptcha')
      case 816:
        return t('SERVER_ERRORS.LOGIN.invalidLogin')
      case 860:
        return t('SERVER_ERRORS.LOGIN.playerSecondSession')
      default:
        return error.message || t('SERVER_ERRORS.LOGIN.serverError')
    }
  }

  return (
    <div className="login-error bo-form-error">
      <div className="login-error__message">
        {getErrorMessage()}
      </div>

      <div className="login-error__params">
        {error.code && <span>{t('SERVER_ERRORS.errorCode', { value: error.code.toString() })}</span>}
        {error.extraData?.traceId && <span>{t('SERVER_ERRORS.traceId', { value: error.extraData.traceId })}</span>}
      </div>
    </div>
  )
}
