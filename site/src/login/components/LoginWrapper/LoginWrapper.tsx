import type { PropsWithChildren } from 'react'
import { useDeviceStore } from '@/hooks/useDeviceStore'
import './LoginWrapper.scss'

interface Props {
  className?: string
}

export const LoginWrapper = ({ children, className = '' }: PropsWithChildren<Props>) => {
  const { isMobile } = useDeviceStore()

  return (
    <div className={`login ${className} ${isMobile ? 'mobile' : ''}`}>
    {/* Language selector would go here */}
    <div className="login__dialog bo-login-dialog">
      {children}
    </div>
  </div>
  )
}
