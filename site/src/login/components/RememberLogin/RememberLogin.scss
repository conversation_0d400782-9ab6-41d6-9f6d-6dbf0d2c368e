.checkbox {
    position: relative;
    display: inline-block;
    width: 18px;
    height: 18px;
    margin: 0 5px 0 0;
    padding: 0;
    line-height: 17px;
    font-weight: normal;
    border: 1px solid #505050;
    border-radius: 3px;
    background: transparent;
    cursor: pointer;
    transition: border 0.15s ease-in-out;

    &.active {
      border-color: #fff;
    }

    &:hover {
      border-color: #fff;
    }
    
    &__input {
      visibility: hidden;
      width: 0;
      height: 0;

      &:checked+.checkbox__area:after {
        opacity: 1;
      }
    }

    &__area {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      &:after {
        content: "\2714";
        display: block;
        font-family: 'live-fonts';
        font-size: 12px;
        line-height: 1;
        opacity: 0;
      }
    }
  }