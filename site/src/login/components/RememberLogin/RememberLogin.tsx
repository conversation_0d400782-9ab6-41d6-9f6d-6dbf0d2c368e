import React, { useState, useEffect } from 'react'
import { useTranslate } from '@/hooks/useTranslate'
import './RememberLogin.scss'

interface Props {
  playerCode?: string
  className?: string
}

// Function to detect private/incognito mode
const detectPrivateMode = (): boolean => {
  try {
    localStorage.setItem('test', '1')
    localStorage.removeItem('test')
    return false
  } catch {
    return true
  }
}

export const RememberLogin = ({ playerCode, className }: Props) => {
  const isPrivateMode = detectPrivateMode()
  const [isRemembered, setIsRemembered] = useState<boolean>(localStorage.getItem('LOGIN_REMEMBERED') ? true : false)
  const { t } = useTranslate()

  useEffect(() => {
    if (isRemembered && playerCode) {
      localStorage.setItem('LOGIN_REMEMBERED', playerCode)
    } else if (!isRemembered) {
      localStorage.removeItem('LOGIN_REMEMBERED')
    }
  }, [isRemembered, playerCode])

  if (isPrivateMode) {
    return null
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsRemembered(e.target.checked)
  }

  return (
    <div className={className}>
      <label htmlFor="rememberLogin">
        <span className={`checkbox bo-input ${isRemembered ? 'active' : ''}`}>
          <input
            type="checkbox"
            id="rememberLogin"
            name="rememberLogin"
            className="checkbox__input"
            checked={isRemembered}
            onChange={handleChange}
          />
          <span className="checkbox__area" />
        </span>
        {t('LOGIN.rememberLogin')}
      </label>
    </div>
  )
}
