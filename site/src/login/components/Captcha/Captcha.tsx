import { useTranslate } from '@/hooks/useTranslate'
import type { CaptchaData } from '@/services/terminalApi'
import { getTerminalApi } from '@/services/terminalApi'
import type { MouseEvent } from 'react'
import './Captcha.scss'

interface Props {
  captcha?: CaptchaData
  setCaptchaToken: (value: string) => void
  setCaptcha: (captcha?: CaptchaData) => void
  playerCode: string
}

export const Captcha = ({ captcha, setCaptchaToken, setCaptcha, playerCode }: Props) => {
  const { t } = useTranslate()

  const onRefreshCaptcha = (event?: MouseEvent): void => {
    if (event) {
      event.preventDefault()
    }
    getTerminalApi().refreshCaptcha(playerCode)
      .then(setCaptcha)
      .catch(error => {
        console.error('Failed to refresh captcha:', error)
      })
  }

  if (!captcha) {
    return null
  }

  return (
    <>
      <div className="captcha">
        <img src={captcha.image} alt="Captcha" />
        <div className="captcha__refresh" onClick={onRefreshCaptcha}>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
            <path d="M27.802 5.197A15.958 15.958 0 0 0 15.999 0c-8.837 0-16 7.163-16 16h3c0-7.18 5.82-13 13-13 3.844 0 7.298 1.669 9.678 4.322L20.999 12h11V1l-4.198 4.197zM29 16c0 7.18-5.82 13-13 13a12.965 12.965 0 0 1-9.678-4.322L11 20H0v11l4.197-4.197A15.958 15.958 0 0 0 16 32c8.837 0 16-7.163 16-16h-3z"/>
          </svg>
        </div>
      </div>
      <div className="login-form__control login-form__control--captcha">
        <input
          type="text"
          className="input bo-input"
          name="captcha"
          onChange={(e) => setCaptchaToken(e.target.value)}
          autoComplete="false"
          placeholder={t('LOGIN.captchaPlaceholder')}
        />
      </div>
    </>
  )
}
