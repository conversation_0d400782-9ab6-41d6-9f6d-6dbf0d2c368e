import { useTranslate } from '@/hooks/useTranslate'
import type { ValidationError } from '@/utils/stringValidators'
import { StringValidators } from '@/utils/stringValidators'
import { InputField } from './InputField'

interface Props {
  value: string
  onValue: (value: string) => void
  onErrors?: (errors: ValidationError[]) => void
  onTouched?: () => void
  currentPassword: string
  errors?: ValidationError[]
}

export const NewPasswordField = ({ currentPassword, ...props }: Props) => {
  const { t } = useTranslate()

  return (
    <InputField
      name="newPassword"
      placeholder={t('PASSWORD_CHANGE.passwordNew')}
      autoComplete="new-password"
      validators={[
        StringValidators.Required(),
        StringValidators.MinLength(),
        StringValidators.ContainsDigit(),
        StringValidators.ContainsLowercase(),
        StringValidators.ContainsUppercase(),
        StringValidators.Equal(currentPassword)
      ]}
      {...props}
    />
  )
}
