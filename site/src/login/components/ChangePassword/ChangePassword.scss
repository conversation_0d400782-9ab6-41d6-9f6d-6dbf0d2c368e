@use '../../../styles/variables';

.change-password {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 35px 20px;

    &__title {
      margin-bottom: 25px;
      text-align: center;
    }
    &__wrapper {
      display: flex;
    }
    &__form {
      width: 280px;
    }
    &__requirements {
      width: 280px;
      margin-left: 20px;
    }
    &__controls {
      display: flex;
      margin-top: 30px;
    }
    &__control {
      &:last-child {
        margin-bottom: 0;
      }
    }
    &__server-error {
      color: variables.$color-error;
      margin-top: 0;
    }
    &__control {
      position: relative;
      margin-bottom: 20px;
    }
    &__input {
      width: 100%;
      color: white;
      font-size: 14px;
      font-weight: 400;
      height: 36px;
      padding: 5px 15px;
      border-radius: 10px;
      box-shadow: inset 0 0 10px 0 rgba(0, 0, 0, 1);
      background: #232323;
      border: 1px solid #42393c;
    }

    .mobile & {
      padding: 20px;
      overflow: auto;

      @media (orientation: portrait) {
        height: 100%;
        &__wrapper {
          width: 100%;
          flex-direction: column;
        }
        &__form {
          width: 100%;
          max-width: 100%;
        }
        &__requirements {
          width: 100%;
          margin-left: 0;
          margin-top: 20px;
        }
        &__controls {
          margin-top: 20px;
          .btn {
            font-size: 13px;
          }
        }
        &__title {
          margin-bottom: 15px;
        }
      }
    }
  }

  .requirements {
    padding: 5px 15px;
    border-radius: 10px;

    &__title {
      color: #fff;
      margin-top: 10px;
      margin-bottom: 15px;
    }

    &__item {
      position: relative;
      padding-left: 24px;
      margin-bottom: 5px;

      svg {
        position: absolute;
        left: 0;
        top: 0;
        height: 14px;
        fill: #fff;
      }

      &--error {
        color: variables.$color-error;

        svg {
          fill: variables.$color-error;
        }
      }
    }
  }
  
  .control-error {
    position: absolute;
    bottom: -14px;
    left: 15px;
    font-size: 11px;
    line-height: 1;
    color: variables.$color-error;
    text-align: left;
  }