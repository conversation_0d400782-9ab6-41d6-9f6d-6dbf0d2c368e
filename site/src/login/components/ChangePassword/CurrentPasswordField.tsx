import { useTranslate } from '@/hooks/useTranslate'
import type { ValidationError } from '@/utils/stringValidators'
import { StringValidators } from '@/utils/stringValidators'
import { InputField } from './InputField'

interface Props {
  value: string
  onValue: (value: string) => void
  onErrors?: (errors: ValidationError[]) => void
}

export const CurrentPasswordField = (props: Props) => {
  const { t } = useTranslate()

  return (
    <InputField
      name="password"
      placeholder={t('PASSWORD_CHANGE.passwordCurrent')}
      autoComplete="current-password"
      validators={[
        StringValidators.Required()
      ]}
      {...props}
    />
  )
}
