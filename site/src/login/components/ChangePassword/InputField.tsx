import { useEffect, useState } from 'react'
import { useTranslate } from '@/hooks/useTranslate'
import type { ValidationError, ValidatorFn } from '@/utils/stringValidators'

interface Props {
  name: string
  value: string
  onValue: (value: string) => void
  placeholder: string
  autoComplete?: string
  validators?: ValidatorFn[]
  onErrors?: (errors: ValidationError[]) => void
  onTouched?: () => void
}

export const InputField = ({
  name,
  value,
  onValue,
  placeholder,
  autoComplete = 'off',
  validators = [],
  onErrors,
  onTouched
}: Props) => {
  const { t } = useTranslate()
  const [touched, setTouched] = useState(false)
  const [errors, setErrors] = useState<ValidationError[]>([])

  const isInvalid = touched && errors.length > 0

  // Функция валидации
  const validate = (value: string, validators: ValidatorFn[]): ValidationError[] => {
    const validationErrors: ValidationError[] = []
    for (const validationFn of validators) {
      const result = validationFn(value)
      if (result) {
        validationErrors.push(result)
      }
    }
    return validationErrors
  }

  // Валидируем при изменении value или validators
  useEffect(() => {
    const validationErrors = validate(value, validators)
    if (JSON.stringify(errors) !== JSON.stringify(validationErrors)) {
      setErrors(validationErrors)
    }
  }, [value, validators, errors])

  // Сообщаем об ошибках наружу
  useEffect(() => {
    if (onErrors) {
      onErrors(errors)
    }
  }, [errors, onErrors])

  const handleBlur = () => {
    setTouched(true)
    if (onTouched) {
      onTouched()
    }
  }

  return (
    <div className="change-password__control">
      <input
        type="password"
        name={name}
        autoComplete={autoComplete}
        className={`change-password__input input ${isInvalid ? 'control-invalid' : ''}`}
        value={value}
        onChange={e => onValue(e.target.value)}
        onBlur={handleBlur}
        placeholder={placeholder}
      />
      {isInvalid && errors[0] && (
        <div className="control-error">
          {t(errors[0].message, errors[0].params ?? {})}
        </div>
      )}
    </div>
  )
}
