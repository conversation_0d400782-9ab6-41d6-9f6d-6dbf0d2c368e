import type { ValidationError } from '@/utils/stringValidators'

interface Props {
  error?: ValidationError
  message: string
  isTouched: boolean
}

export const PasswordRequirement = ({ error, message, isTouched }: Props) => {
  return (
    <div className={`requirements__item ${error && isTouched ? 'requirements__item--error' : ''}`}>
      {error || !isTouched ?
        <svg xmlns="http://www.w3.org/2000/svg" viewBox='0 0 32 32'>
          <path d="M1 12h30a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1v-6a1 1 0 0 1 1-1z"/>
        </svg>
        :
        <svg xmlns="http://www.w3.org/2000/svg" viewBox='0 0 20 20'>
          <path d="m0 11 2-2 5 5L18 3l2 2L7 18z"/>
        </svg>
      }
      <span>{message}</span>
    </div>
  )
}
