import { useTranslate } from '@/hooks/useTranslate'
import { getPlayerApi } from '@/services/playerApi'
import type { LoginPlayerInfo } from '@/services/terminalApi'
import { getError, type ValidationError } from '@/utils/stringValidators'
import type { FormEvent } from 'react'
import { useState } from 'react'
import { LoginWrapper } from '../LoginWrapper/LoginWrapper'
import './ChangePassword.scss'
import { ConfirmPasswordField } from './ConfirmPasswordField'
import { CurrentPasswordField } from './CurrentPasswordField'
import { NewPasswordField } from './NewPasswordField'
import { PasswordRequirement } from './PasswordRequirement'


interface Props {
  loginInfo: LoginPlayerInfo
  onSuccess: () => void
}

export const ChangePassword = ({ loginInfo, onSuccess }: Props) => {
  const { t } = useTranslate()

  const [password, setPassword] = useState('')
  const [passwordErrors, setPasswordErrors] = useState<ValidationError[]>([])

  const [newPassword, setNewPassword] = useState('')
  const [newPasswordErrors, setNewPasswordErrors] = useState<ValidationError[]>([])
  const [isNewPasswordTouched, setIsNewPasswordTouched] = useState(false)

  const [confirmPassword, setConfirmPassword] = useState('')
  const [confirmPasswordErrors, setConfirmPasswordErrors] = useState<ValidationError[]>([])

  const [serverError, setServerError] = useState<string | null>(null)

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault()

    const isValid = !passwordErrors.length && !newPasswordErrors.length && !confirmPasswordErrors.length
    if (!isValid) {
      return
    }

    setServerError(null)
    getPlayerApi(loginInfo.token).changePassword({
      password,
      newPassword,
      confirmPassword
    }).then(onSuccess).catch((error) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to change password'
      setServerError(errorMessage)
    })
  }

  const handleNewPasswordChange = (value: string) => {
    setNewPassword(value)
  }

  return (
    <LoginWrapper className="login--change">
             <div className="change-password">
          <h3 className="change-password__title">{t('PASSWORD_CHANGE.changeRequired')}</h3>
          {serverError && (
            <h4 className="change-password__server-error">{serverError}</h4>
          )}

          <div className="change-password__wrapper">
            <form className="change-password__form" onSubmit={handleSubmit}>
              <CurrentPasswordField
                value={password}
                onValue={setPassword}
                onErrors={setPasswordErrors}
              />

              <NewPasswordField
                value={newPassword}
                onValue={handleNewPasswordChange}
                onErrors={setNewPasswordErrors}
                currentPassword={password}
                onTouched={() => setIsNewPasswordTouched(true)}
              />

              <ConfirmPasswordField
                value={confirmPassword}
                onValue={setConfirmPassword}
                onErrors={setConfirmPasswordErrors}
                newPassword={newPassword}
              />
            </form>

            <div className="change-password__requirements requirements">
              <h4 className="requirements__title">{t('PASSWORD_CHANGE.passwordRequirements')}:</h4>
              <PasswordRequirement
                isTouched={isNewPasswordTouched}
                error={getError(newPasswordErrors, 'minLength')}
                message={t('PASSWORD_CHANGE.minLength')} />
              <PasswordRequirement
                isTouched={isNewPasswordTouched}
                error={getError(newPasswordErrors, 'containLowercase')}
                message={t('PASSWORD_CHANGE.containLowercase', { value: '1' })}
              />
              <PasswordRequirement
                isTouched={isNewPasswordTouched}
                error={getError(newPasswordErrors, 'containUppercase')}
                message={t('PASSWORD_CHANGE.containUppercase', { value: '1' })}
              />
              <PasswordRequirement
                isTouched={isNewPasswordTouched}
                error={getError(newPasswordErrors, 'containDigit')}
                message={t('PASSWORD_CHANGE.containDigit', { value: '1' })}
              />
            </div>
          </div>

          <div className="change-password__controls">
            <button className="btn btn--highlight" onClick={handleSubmit}>
              {t('PASSWORD_CHANGE.saveAndLogin')}
            </button>
          </div>
        </div>
    </LoginWrapper>
  )
}
