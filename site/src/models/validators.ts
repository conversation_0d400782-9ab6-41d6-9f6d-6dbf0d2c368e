export class Validators {
  static MinLength = (minLength: number): ((value: string) => ValidationErrors | null) => (value: string) => {
    if (value.length < minLength) {
      return { minLength: true }
    }
    return null
  }

  static MaxLength = (maxLength: number): ((value: string) => ValidationErrors | null) => (value: string) => {
    if (value.length > maxLength) {
      return { maxLength: true }
    }
    return null
  }

  static NicknamePattern = (): ((value: string) => ValidationErrors | null) => (value: string) => {
    const pattern = /^[A-Za-z\d!@#$%^&*()_+=\-`~\\{}\][|';:/.,?><]*$/
    if (value && !pattern.test(value)) {
      return { notAllowed: true }
    }
    return null
  }

  static NotLogin = (playerInfoCode?: string): ((value: string) => ValidationErrors | null) => (value: string) => {
    if (value && value === playerInfoCode) {
      return { notLogin: true }
    }
    return null
  }

  static Required = (): ((value: string) => ValidationErrors | null) => (value: string) => {
    if (!value) {
      return { required: true }
    }
    return null
  }
}

export interface ValidationErrors {
  notLogin?: boolean;
  pattern?: boolean;
  minLength?: boolean;
  maxLength?: boolean;
  notAllowed?: boolean;
  changeExceeded?: boolean;
  required?: boolean;
}

export function nicknameServerValidationErrors(code?: number): ValidationErrors | null {
  switch (code) {
    case 851:
      return { notLogin: true }
    case 852:
      return { pattern: true }
    case 853:
      return { minLength: true }
    case 854:
      return { maxLength: true }
    case 855:
      return { notAllowed: true }
    case 862:
      return { changeExceeded: true }
    default:
      return null
  }
}
