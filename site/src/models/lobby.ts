import type { GameLiveInfo, LiveTableId } from '@/models/game.ts'
import type { FullPageGameType } from 'sw-fullpage'

export interface LiveManagerEndpoint {
  url: string
  path?: string
  socketPath?: string
}

export interface LobbyInfoOptions {
  lobbyId?: string
  cashierUrl?: string
  disableLogout?: boolean
  enableCloseGame?: boolean
  refreshIntervalLive?: number
  playerInactivityTime?: number
  numberOfPlayers?: {
    show?: boolean
    games?: string[]
  }
}

export interface Ribbon {
  text: string
  bg: string
  color: string
}

export interface FullPageOptions {
  game: GameLiveInfo
  gameType: FullPageGameType
}

export interface MenuItem {
  slug: string
  title: Record<string, string>
  icon?: Record<string, string>
  ribbon?: Record<string, Ribbon>
  tableIds?: LiveTableId[]
  sections?: SectionItem[]
  fullPage?: FullPageOptions
  isGridLayout?: boolean
}

export interface SectionItem {
  id: string
  title: Record<string, string>
  games: GameLiveInfo[]
  showFavoriteGames: boolean
  showRecentGames: boolean
}
