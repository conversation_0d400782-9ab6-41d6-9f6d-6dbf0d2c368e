import type { SwScoreboardPayloadType } from 'sw-scoreboard'
import type { Blackjack, DealerInfo, GameType, TableStatus } from 'sw-live-core'

interface TerminalTableInfo {
  tableId: string;
  tableType: GameType;
  dealer: DealerInfo;
  status: TableStatus;
  scoreboard: string;
  result: SwScoreboardPayloadType;
  language?: string;
  seats?: Blackjack.SeatState[];
  numberOfPlayers: number;
  isMaintenance: boolean;
}

interface TerminalOpenTable {
  tableId: string;
  tableType: GameType;
  dealer: DealerInfo;
  status: TableStatus;
  language?: string;
}

interface TerminalTableScoreboard {
  tableId: string;
  tableType: GameType;
  scoreboard: string;
  result: SwScoreboardPayloadType;
}

interface TerminalTableDealer {
  tableId: string;
  dealer: DealerInfo;
}

interface TerminalTableState {
  tableId: string;
  status: TableStatus;
}

interface TerminalTableSeatUpdate {
  tableId: string;
  tableType: GameType.BLACKJACK;
  seat: Blackjack.PlayerSeat | null;
  previousSeat?: Blackjack.PlayerSeat;
  playerCode: string;
  playerHash: string;
}

interface TerminalCounter {
  tableId: string;
  numberOfPlayers: number;
}

export interface LiveManagerEvents {
  'live-table': (data: TerminalTableInfo) => void
  'live-table-open': (data: TerminalOpenTable) => void
  'live-table-scoreboard': (data: TerminalTableScoreboard) => void
  'live-table-dealer': (data: TerminalTableDealer) => void
  'live-table-state': (data: TerminalTableState) => void
  'live-table-seat': (data: TerminalTableSeatUpdate) => void
  'live-table-players': (data: TerminalCounter) => void
  'reconnect_attempt': (attemptNumber: number) => void
}
