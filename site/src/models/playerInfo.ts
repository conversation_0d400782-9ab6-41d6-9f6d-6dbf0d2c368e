export interface PlayerTokenPayload {
  readonly playerCode: string
  readonly currency?: string
}

interface PlayerBalance {
  readonly main: number
}

export interface PlayerData {
  readonly code: string
  readonly nickname?: string
  readonly historyUrl?: string
  readonly isVip?: boolean
  readonly currency: string
  readonly language?: string
  readonly balances?: Record<string, PlayerBalance>
}

export interface FavoriteGameData {
  readonly gameCode: string
  readonly isFavorite?: boolean
}

export interface RecentlyGameData {
  readonly gameCode: string
  readonly time: number
}

export interface PlayerInfo extends Omit<PlayerData, 'balances'> {
  readonly balance?: number
}
