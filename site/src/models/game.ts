import type { Ribbon } from './lobby.ts'
import type { GameType } from 'sw-live-core'
import type { SwScoreboardPayloadType } from 'sw-scoreboard'

export interface GameLaunchData {
  code: string
  url: string
  title?: string
  tableId?: string
  language?: string
}

export interface ActiveGame extends GameLaunchData {
  minimized?: boolean
  isLive?: boolean
}

export interface SeatsInfo {
  seats: TableSeats
  haveVacantSeat: boolean
  countOccupiedSeats: number
  countPlayerSeats: number
}

export interface LiveTableId {
  provider: string
  tableId: string
}

export interface LiveTableInfo extends LiveTableId {
  gameCode?: string
  liveGameType?: GameType
  multiSeatLimit?: number
  isOnline?: boolean
  dealer?: GameLiveDealer
  numberOfPlayers?: number
  scoreboard?: SwScoreboardPayloadType
  limits: GameLiveInfoLimit[]
  isAtom?: boolean
}

export enum BlackjackSeatType {
  Player = 'player',
  Vacant = 'vacant',
  Occupied = 'occupied'
}

export interface TableSeats {
  P1: BlackjackSeatType
  P2: BlackjackSeatType
  P3: BlackjackSeatType
  P4: BlackjackSeatType
  P5: BlackjackSeatType
  P6: BlackjackSeatType
  P7: BlackjackSeatType
}

export interface GameLiveInfo {
  code: string
  table?: LiveTableInfo
  title?: Record<string, string>
  isLiveGame?: boolean
  isOnline?: boolean
  ribbon?: Record<string, Ribbon>
  overlayUrl?: Record<string, string>
  language?: string
  playedDate?: string
  poster?: string
}

export interface GameLiveInfoLimit {
  name: string
  stakeMax: number
  stakeMin: number
  isDefaultRoom: boolean
}

export interface GameLiveDealer {
  name?: string
  picture?: string
}

