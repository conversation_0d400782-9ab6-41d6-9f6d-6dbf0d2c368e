export enum MessageType {
  OpenLobby = 'openLobby',
  OpenCashier = 'openCashier',
  OpenGameHistory = 'openGameHistory',
  Close = 'close',
  MinimizeGame = 'minimizeGame',
  ExpandGame = 'expandGame',
  ChooseGame = 'chooseGame',
  GameStartedLoading = 'gameStartedLoading'
}

export enum GestureType {
  SwipeUp = 'swipeUp',
  SwipeDown = 'swipeDown'
}

export interface MessageData {
  type: MessageType
  payload?: unknown,
  gesture: GestureType
  fullscreen: boolean
}
