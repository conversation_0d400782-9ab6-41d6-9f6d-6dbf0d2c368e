import { IconsSprite } from './components/Icon/IconsSprite.tsx'
import { RouterStore } from './components/RouterStore.tsx'
import { AppRouter } from './components/AppRouter.tsx'
import { TranslateStore } from './components/TranslateStore.tsx'
import { ErrorBoundary } from './components/ErrorBoundary/ErrorBoundary.tsx'
import { DeviceProvider } from './components/DeviceStore.tsx'

const App = () => {
  return (
    <>
      <DeviceProvider>
        <TranslateStore>
          <ErrorBoundary>
            <RouterStore>
              <IconsSprite />
              <AppRouter />
            </RouterStore>
          </ErrorBoundary>
        </TranslateStore>
      </DeviceProvider>
    </>
  )
}

export default App
