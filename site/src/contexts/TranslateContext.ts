import { createContext } from 'react'

export interface TranslateStoreType {
  language: string
  saveLanguage: (value: string) => void
  detectLanguage: (initiator: string, queryLang?: string | null) => string
  translate: <T>(data?: Record<string, T>) => T | undefined
  t: (path: string, params?: Record<string, string>) => string
}

export const TranslateContext = createContext<TranslateStoreType | null>(null)
