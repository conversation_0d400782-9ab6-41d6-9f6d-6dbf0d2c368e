import { createContext } from 'react'
import type { NavigationExtras } from '../models/router.ts'

export interface Route {
  gameCode?: string
  ticket?: string
  name?: string
}

export interface RouterStoreType {
  currentPath: string
  searchParams: URLSearchParams
  route: Route
  navigate: (path?: string, extras?: NavigationExtras) => void
}

export const RouterStoreContext = createContext<RouterStoreType | null>(null)
