import { BaseApi } from './baseApi'
import type { Blackjack } from 'sw-live-core'
import type { LiveManagerEndpoint } from '../models/lobby.ts'

export interface BlackjackSeatBody {
  provider: string
  tableId: string
  nickname?: string
  isVip?: boolean
  seat?: Blackjack.PlayerSeat
  previousSeat?: Blackjack.PlayerSeat
}

interface ConfirmResponse {
  confirmed: boolean;
}

class LiveApi extends BaseApi {

  constructor(url: string, token: string) {
    super(url, { 'x-player-token': token })
  }

  toggleBlackjackSeat({ tableId, provider, ...body }: BlackjackSeatBody) {
    return this.fetch<ConfirmResponse>(`/tables/${provider}/${tableId}/seat`, {
      method: 'POST',
      body: JSON.stringify(body)
    })
  }
}

export function getLiveApi(endpoint: LiveManagerEndpoint | undefined | null, token: string | undefined | null) {
  const baseUrl = endpoint?.url ? `${endpoint.url}/${endpoint.path ? `${endpoint.path}/` : ''}v2` : undefined
  if (baseUrl && token) {
    return new LiveApi(baseUrl, token)
  }
}
