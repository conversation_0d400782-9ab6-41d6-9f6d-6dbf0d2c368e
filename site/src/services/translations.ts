import en from '../i18n/en.json'
import zhCn from '../i18n/zh-cn.json'
import zhTw from '../i18n/zh-tw.json'
import ja from '../i18n/ja.json'
import ms from '../i18n/ms.json'
import ko from '../i18n/ko.json'
import th from '../i18n/th.json'
import vi from '../i18n/vi.json'
import id from '../i18n/id.json'
import ro from '../i18n/ro.json'
import it from '../i18n/it.json'
import el from '../i18n/el.json'
import km from '../i18n/km.json'
import es from '../i18n/es.json'
import pt from '../i18n/pt.json'
import ptBr from '../i18n/pt-br.json'
import ru from '../i18n/ru.json'
import de from '../i18n/de.json'
import sv from '../i18n/sv.json'
import da from '../i18n/da.json'
import nl from '../i18n/nl.json'
import bg from '../i18n/bg.json'
import sr from '../i18n/sr.json'
import tr from '../i18n/tr.json'

export interface TranslationRecord {
  [key: string]: TranslationValue
}

export type TranslationValue = string | TranslationRecord

interface TranslationData {
  codes?: string[];
  translations: TranslationRecord;
}

export const DEFAULT_LANGUAGE = 'en'

export const TRANSLATIONS: Record<string, TranslationData> = {
  en: {
    codes: ['en-gb', 'en-us'],
    translations: en
  },
  tr: {
    translations: tr
  },
  'zh-cn': {
    codes: ['ch', 'zh-cn'],
    translations: zhCn
  },
  'zh-tw': {
    translations: zhTw
  },
  ja: {
    translations: ja
  },
  ms: {
    translations: ms
  },
  ko: {
    codes: ['ko-kr'],
    translations: ko
  },
  th: {
    translations: th
  },
  vi: {
    translations: vi
  },
  id: {
    translations: id
  },
  ro: {
    translations: ro
  },
  it: {
    translations: it
  },
  el: {
    translations: el
  },
  km: {
    translations: km
  },
  es: {
    translations: es
  },
  pt: {
    translations: pt
  },
  ['pt-br']: {
    translations: ptBr
  },
  ru: {
    translations: ru
  },
  de: {
    translations: de
  },
  sv: {
    translations: sv
  },
  da: {
    translations: da
  },
  nl: {
    translations: nl
  },
  bg: {
    translations: bg
  },
  sr: {
    translations: sr
  }
}

export const LANGUAGE_CODES = Object.entries(TRANSLATIONS).reduce<Record<string, string>>((result, [id, { codes }]) => ({
  ...result,
  [id]: id, ...(codes ?? []).reduce<Record<string, string>>((data, code) => ({
    ...data,
    [code]: id
  }), {})
}), {})
