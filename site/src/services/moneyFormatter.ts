import type { IFormatNumberConfig } from '@skywind-group/sw-money-formatter'
import { MoneyFormatter } from '@skywind-group/sw-money-formatter'
import { getPlayerCurrency } from '@/utils/playerInfo.ts'
import type { GameLiveInfoLimit } from '@/models/game.ts'

const moneyFormatter = new MoneyFormatter()
moneyFormatter.config = {
  ...moneyFormatter.config,
  showCurrency: false
}

export function getMoneyFormatter() {
  moneyFormatter.setupCurrency(getPlayerCurrency())
  return {
    formatGameLimits(value: GameLiveInfoLimit) {
      const stakeMin = moneyFormatter.formatNumber(value.stakeMin, { showCurrency: false })
      const stakeMax = moneyFormatter.formatNumber(value.stakeMax, { showCurrency: false })
      return `${(moneyFormatter.getFormattedCurrencySymbol())} ${stakeMin} - ${stakeMax}`
    },
    get currencySymbol() {
      return moneyFormatter.getFormattedCurrencySymbol()
    },
    formatNumber(value: number, config?: IFormatNumberConfig) {
      return moneyFormatter.formatNumber(value, config)
    }
  }
}
