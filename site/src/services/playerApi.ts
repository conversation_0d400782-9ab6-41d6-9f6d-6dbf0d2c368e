import { <PERSON><PERSON><PERSON> } from './baseApi'
import type { LiveManagerEndpoint, LobbyInfoOptions, MenuItem } from '../models/lobby'
import type { GameLiveInfo } from '../models/game.ts'
import type { ExtendedGameUrlInfo, LobbyData } from '../utils/lobby.ts'
import { buildMenuItems } from '../utils/lobby.ts'
import type { PlayerInfo } from '@/models/playerInfo.ts'
import { TokenIsMissing } from '@/models/error.ts'

interface LobbyInfo {
  liveManagerEndpoint?: LiveManagerEndpoint
  menuItems: MenuItem[]
  options: LobbyInfoOptions
  title?: string
}

interface LoginPlayerInfo {
  code?: string
  token: string
  isPasswordTemp?: boolean
}

interface PlayerPasswordChange {
  password: string
  newPassword: string
  confirmPassword: string
}

const PLAYER_API = '/api/player'

function getLobbyOptions<T = string>(name: string, options?: { key: string, value: unknown }[]): T | undefined {
  const value = options?.find(({ key }) => key === name)?.value
  return value === undefined ? undefined : value as T
}

function parseRefreshIntervalLive(options?: { key: string, value: unknown }[]) {
  const value = getLobbyOptions<string>('refreshIntervalLive', options)
  const interval = typeof value === 'string' ? parseInt(value, 10) : value
  return (interval && interval > 0) ? interval * 1000 : undefined
}

class PlayerApi extends BaseApi {

  constructor(token: string) {
    super(PLAYER_API, { 'x-player-token': token })
  }

  async lobbyInfo(lobbyId: string): Promise<LobbyInfo> {
    const data = await this.fetch<LobbyData>(`/lobbies/${lobbyId}`, {
      params: {
        fields: 'info.menuItems,info.liveManagerUrl,info.options,title',
        includeGamesLimits: 'true',
        socketVersion: 'v4'
      }
    })

    return ({
      title: data?.title,
      liveManagerEndpoint: data?.info?.liveManagerUrl,
      menuItems: buildMenuItems(data?.info?.menuItems),
      options: {
        cashierUrl: getLobbyOptions<string>('cashierUrl', data?.info?.options),
        enableCloseGame: !(getLobbyOptions<boolean>('disableCloseGame', data?.info?.options) ?? false),
        refreshIntervalLive: parseRefreshIntervalLive(data?.info?.options),
        playerInactivityTime: getLobbyOptions<number>('playerInactivityTime', data?.info?.options),
        numberOfPlayers: {
          show: getLobbyOptions<boolean>('numberOfPlayers_show', data?.info?.options) ?? false,
          games: getLobbyOptions<string[]>('numberOfPlayers_games', data?.info?.options)
        }
      }
    })
  }

  getGameUrlInfo(gameCode: string, game?: GameLiveInfo) {
    const fields = `url,newPlayerToken,historyUrl${game ? 'game.features.live.tableId,game.title,game.features.translations' : ''}`
    return this.fetch<ExtendedGameUrlInfo>(`/games/${gameCode}`, { params: { fields } })
  }

  refresh() {
    return this.fetch<LoginPlayerInfo>('/refresh', {
      method: 'POST',
      body: JSON.stringify({})
    })
  }

  async updateFavoriteGame(gameCode: string, isFavorite: boolean) {
    try {
      if (isFavorite) {
        await this.postFavoriteGame(gameCode)
      } else {
        await this.deleteFavoriteGame(gameCode)
      }
      return true
    } catch (e) {
      console.error(e)
      return false
    }
  }

  deleteFavoriteGame(gameCode: string) {
    return this.fetch<void>(`/favoritegames/${gameCode}`, {
      method: 'DELETE'
    })
  }

  postFavoriteGame(gameCode: string) {
    return this.fetch<void>(`/favoritegames/${gameCode}`, {
      method: 'POST'
    })
  }

  changeNickname(nickname: string) {
    return this.fetch<PlayerInfo>('/info', {
      method: 'PATCH',
      body: JSON.stringify({ nickname })
    })
  }

  changePassword(data: PlayerPasswordChange) {
    return this.fetch<PlayerInfo>('/password', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }
}

export function getPlayerApi(token: string | undefined | null) {
  if (!token) {
    throw new TokenIsMissing()
  }
  return new PlayerApi(token)
}
