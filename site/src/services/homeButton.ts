function parentMessage(message: unknown) {
  if (window.top && window.top !== window.self) {
    window.top.postMessage(message, '*')
    if (window.top !== window.parent) {
      window.parent.postMessage(message, '*')
    }
    console.info('[lobby] Message to parent window', message)
  }
}

export class HomeButton {
  public readonly showOnDesktop: boolean
  public readonly showOnMobile: boolean
  public readonly swmp: boolean

  constructor(onDesktop: boolean, swmp: boolean, private readonly url?: string | null) {
    this.swmp = swmp
    this.showOnMobile = Boolean(this.url)
    this.showOnDesktop = onDesktop && this.showOnMobile
  }

  open() {
    if (this.swmp) {
      parentMessage(JSON.stringify({ msgId: 'sw2opLobby' }))
    } else if (this.url) {
      window.location.href = this.url
    }
  }
}
