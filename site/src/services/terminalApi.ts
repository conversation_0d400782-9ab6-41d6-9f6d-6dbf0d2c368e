import type { LobbyInfoOptions } from '@/models/lobby'
import { BaseApi } from './baseApi'

interface LoginPlayerData {
  code: string
  password: string
  captchaToken?: string
  csrfToken?: string
  force?: boolean
}

export interface LoginPlayerInfo {
  lobbyId: string
  code?: string
  token: string
  isPasswordTemp?: boolean
}

export interface CaptchaData {
  csrfToken: string
  image: string
}

class Terminal<PERSON>pi extends BaseApi {

  constructor() {
    super('/api/terminal')
  }

  login(data: LoginPlayerData) {
    return this.fetch<LoginPlayerInfo>('/players/login', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  options() {
    return this.fetch<LobbyInfoOptions>('/options')
  }

  externalLogin(ticket: string) {
    return this.fetch<LoginPlayerInfo>('/players/external/login', {
      method: 'POST',
      body: JSON.stringify({ ticket })
    })
  }

  refreshCaptcha(playerCode: string) {
    return this.fetch<CaptchaData>(`/refresh-captcha/${playerCode}`)
  }
}

export function getTerminalApi() {
  return new TerminalApi()
}
