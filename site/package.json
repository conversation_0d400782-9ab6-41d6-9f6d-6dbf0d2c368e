{"name": "site", "version": "1.4.0", "type": "module", "scripts": {"clean": "rm -rf node_modules package-lock.json dist", "prebuild": "tsc -b", "build": "VITE_APP_VERSION=\"v${npm_package_version} $( git log --pretty=format:'%h' -n 1) $(date)\" vite build", "dev": "env-cmd -e qa vite", "dev:stage": "env-cmd -e stage vite", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview"}, "dependencies": {"@skywind-group/sw-money-formatter": "0.4.3", "jwt-decode": "^4.0.0", "lodash-es": "^4.17.21", "react": "^19.0.0", "react-dom": "^19.0.0", "slugify": "^1.6.6", "socket.io-client": "^4.8.1", "sw-fullpage": "*", "sw-scoreboard": "*", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/lodash-es": "^4.17.12", "@types/react": "^19.0.4", "@types/react-dom": "^19.0.2", "@types/socket.io-client": "^3.0.0", "@vitejs/plugin-react": "^4.3.4", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "env-cmd": "^10.1.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "npm-run-all": "^4.1.5", "sass-embedded": "^1.83.4", "sw-live-core": "*", "typescript": "^5.7.3", "typescript-eslint": "^8.23.0", "vite": "^6.1.0"}, "private": true}