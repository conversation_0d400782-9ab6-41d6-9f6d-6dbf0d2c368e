import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { fileURLToPath, URL } from 'node:url'
import { dirname, resolve } from 'node:path'

const __dirname = dirname(fileURLToPath(import.meta.url))

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      '/api/terminal': {
        target: process.env.VITE_TERMINAL_API,
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api\/terminal/, `terminals/lobbies/${process.env.VITE_LOBBY_ID}`)
      },
      '/api/player': {
        target: process.env.VITE_PLAYER_API,
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api\/player/, '')
      },
      '/widgets': {
        target: 'https://lobby-res.ss211208.com',
        changeOrigin: true
      },
      '/theme.css': {
        target: `${process.env.VITE_TERMINAL_API}/terminals/lobbies/${process.env.VITE_LOBBY_ID}/theme`,
        changeOrigin: true,
        rewrite: path => path.replace(/^\/theme\.css/, '')
      },
      '/image': {
        target: process.env.VITE_TERMINAL_API,
        changeOrigin: true,
        rewrite: path => path.replace(/\.[^/.]+$/, '').replace(/^\/image/, `terminals/lobbies/${process.env.VITE_LOBBY_ID}/image`)
      }
    }
  },
  build: {
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
        login: resolve(__dirname, 'login/index.html'),
      },
    },
  },
  optimizeDeps: {
    exclude: ['sw-scoreboard', 'sw-fullpage', 'sw-live-core']
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  }
})
