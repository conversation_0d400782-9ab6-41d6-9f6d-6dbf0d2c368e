<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <link rel="icon" type="image/png" href="./favicon.ico" />
  <title>Login Form</title>
  <style>
    html {
      font-family: Arial, sans-serif;
    } 
    html, body {
      background-color: #000;
    }
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      padding: 20px;
    }
    .login-form {
      background-color: #333;
      padding: 2em;
      border-radius: 1em;
      box-shadow: 0 0 1em rgba(0, 0, 0, 0.5);
      max-width: 90%;
    }
    .login-form__control {
      margin-bottom: 1.5em;
    }
    .login-form__label {
      color: #c4c4c4;
      font-size: 0.75em;
      font-weight: 500;
      margin-bottom: 0.5em;
      text-transform: uppercase;
    }
    .login-form__select,
    .login-form__textarea,
    .login-form__input {
      background-color: #444;
      color: #fff;
      padding: 0.5em;
      border: none;
      border-radius: 0.5em;
      width: 100%;
    }
    .login-form__select {
      height: 2em;
    }
    .login-form__textarea {
      height: 10em;
      resize: none;
    }
    .login-form__button {
      background-color: #666;
      color: #fff;
      padding: 0.5em 1em;
      border: none;
      border-radius: 0.5em;
      cursor: pointer;
    }   
  </style>
</head>
<body>
<form class="login-form" id="loginForm">
  <div class="login-form__control">
    <label class="login-form__label" for="env">Env:</label>
    <select class="login-form__select" id="env" name="env" required>
      <option selected value="qa">QA</option>
      <option value="stage">STAGE</option>
    </select>
  </div>
  <div class="login-form__control">
    <label class="login-form__label" for="token">Terminal token:</label>
    <textarea class="login-form__textarea" id="token" name="token" required>eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJsb2JieUlkIjoiNFJrd3I2UmUiLCJwbGF5ZXJVcmwiOiJodHRwczovL3BsYXllci1hcGkucWEtc2VydmVyLmRldi1xYS5zczIxMTIwOC5jb20vdjEiLCJwbGF5ZXJTb2NrZXRVcmwiOiJodHRwczovL3BsYXllci1hcGkucWEtc2VydmVyLmRldi1xYS5zczIxMTIwOC5jb20iLCJ0ZXJtaW5hbFVybCI6Imh0dHBzOi8vdGVybWluYWwtYXBpLnFhLXNlcnZlci5kZXYtcWEuc3MyMTEyMDguY29tL3YxIiwic29ja2V0VXJsIjoiaHR0cHM6Ly9jbGllbnQtYXBpLWRldi5hdzIxMDgxNi5jb20iLCJhbmFseXRpY3NVcmwiOiJodHRwczovL2FuYWx5dGljcy5wdWJsaWMuc2t5d2luZHNlcnZpY2VzLmNvbToxMjQyNCIsImFuYWx5dGljc1NvY2tldFVybCI6Imh0dHBzOi8vY2xpZW50LWFwaS1kZXYuYXcyMTA4MTYuY29tIiwib3B0aW9ucyI6eyJoaWdobGlnaHQiOiIjRkZDRjAwIiwicmVmcmVzaEludGVydmFsTGl2ZSI6IjE1IiwiZ2FtZUxhdW5jaE1vZGUiOnsicHdhTW9iaWxlIjoibW9kYWwiLCJwd2FEZXNrdG9wIjoibW9kYWwifX0sImJyYW5kSWQiOjI3NDMxLCJvcGVyYXRvclR5cGUiOiJicmFuZCIsImxvYmJ5VXJsIjoid3M6Ly9saXZlLW1hbmFnZXItYXBpLnFhLXNlcnZlci5kZXYtcWEuc3MyMTEyMDguY29tIiwiZGVmYXVsdEN1cnJlbmN5IjoiVFJZIiwiZGVmYXVsdExhbmd1YWdlIjoidHIiLCJlbnYiOiJmYWxjb24tY2QyIiwiaWF0IjoxNzM4MDQ1MjM2LCJpc3MiOiJza3l3aW5kZ3JvdXAifQ.UtYvto4DZZ5AjaB47d_JwgIFkzmrrkp0ianreemEWW_Xe8xhiVyERLvgbUw0zxHT1JWa56r9YZKeZMx1v7hyqw</textarea>
  </div>
  <div class="login-form__control">
    <label class="login-form__label" for="lobbyId">lobbyId:</label>
    <input class="login-form__input" type="text" id="lobbyId" name="lobbyId" required value="4Rkwr6Re">
  </div>
  <div class="login-form__control">
    <label class="login-form__label" for="code">Username:</label>
    <input class="login-form__input" type="text" id="code" name="code" required>
  </div>
  <div class="login-form__control">
    <label class="login-form__label" for="password">Password:</label>
    <input class="login-form__input" type="password" id="password" name="password" required>
  </div>
  <button id="button_login" class="login-form__button" type="button">Login</button>
  <p>&nbsp;</p>
  <div class="login-form__control">
    <label class="login-form__label" for="secretKey">Secret key:</label>
    <textarea class="login-form__textarea" id="secretKey" name="secretKey">{"secretKey": "53a0d3ea-db65-4137-b894-7f1957063e9a","username": "as_m1_01","password": "123456qaZ"}</textarea>
  </div>
  <div class="login-form__control">
    <label class="login-form__label" for="gameCode">Game code:</label>
    <input class="login-form__input" type="text" id="gameCode" name="gameCode" value="sw_jw_training">
  </div>
  <div class="login-form__control">
    <label class="login-form__label" for="playerCode">Player code:</label>
    <input class="login-form__input" type="text" id="playerCode" name="playerCode" value="as_test_USD">
  </div>
  <div class="login-form__control">
    <label class="login-form__label" for="merchantCode">Merchant code:</label>
    <input class="login-form__input" type="text" id="merchantCode" name="merchantCode" value="asmerch1">
  </div>
  <div class="login-form__control">
    <label class="login-form__label" for="language">Language code:</label>
    <input class="login-form__input" type="text" id="language" name="language">
  </div>
  <div class="login-form__control">
    <label class="login-form__label" for="cashier">cashier site:</label>
    <input class="login-form__input" type="text" id="cashier" name="cashier">
  </div>
  <div class="login-form__control">
    <label class="login-form__label" for="lobby">lobby site:</label>
    <input class="login-form__input" type="text" id="lobby" name="lobby">
  </div>
  <div class="login-form__control">
    <input type="checkbox" id="performance" name="performance">
    <label class="login-form__label" for="performance">Enable Performance Mode</label>
  </div>
  <button id="button_launch" class="login-form__button" type="button">Launch game</button>
  <button id="button_ticket" class="login-form__button" type="button">Ticket login</button>
</form>

<script>
  document.getElementById('button_login').addEventListener('click', async function(e) {
    e.preventDefault()
    const selectedEnv = document.getElementById('env').value
    if (!selectedEnv) {
      alert('Please select a env')
      return
    }
    const token = document.getElementById('token').value
    const lobbyId = document.getElementById('lobbyId').value
    const code = document.getElementById('code').value
    const password = document.getElementById('password').value

    let terminalApi = 'terminal-api.qa-server.dev-qa.ss211208.com'
    if (selectedEnv === 'stage') {
      terminalApi = 'terminal.gcpstg.m27613.com'
    }
    const response = await fetch(`https://${terminalApi}/v1/terminals/players/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-terminal-token': token
      },
      body: JSON.stringify({
        code,
        password
      })
    })
    if (!response.ok) {
      alert(`Failed to login: ${await response.text()}`)
      return
    }
    const data = await response.json()
    localStorage.removeItem('language')
    localStorage.setItem('playerToken', data.token)
    localStorage.setItem('lobbyId', lobbyId)
    window.location.href = '/'
  })
  
  const performanceFlag = document.getElementById('performance').checked
  const language = document.getElementById('language').value
  const gameCode = document.getElementById('gameCode').value

  document.getElementById('button_launch').addEventListener('click', async function(e) {
    e.preventDefault()

    const selectedEnv = document.getElementById('env').value
    if (!selectedEnv) {
      alert('Please select a env')
      return
    }
    const secretKey = document.getElementById('secretKey').value
    if (!secretKey) {
      alert('Please secretKey')
      return
    }
    const gameCode = document.getElementById('gameCode').value
    if (!gameCode) {
      alert('Please gameCode')
      return
    }
    const playerCode = document.getElementById('playerCode').value
    if (!playerCode) {
      alert('Please playerCode')
      return
    }
    const merchantCode = document.getElementById('merchantCode').value
    if (!merchantCode) {
      alert('Please merchantCode')
      return
    }

    let managementApi = 'management-api.qa-server.dev-qa.ss211208.com'
    if (selectedEnv === 'stage') {
      managementApi = 'api-management-as.ss211208.com'
    }
    const loginResponse = await fetch(`https://${managementApi}/v1/login`, {
      headers: {
        'Content-Type': 'application/json'
      },
      method: 'POST',
      body: secretKey
    })
    if (!loginResponse.ok) {
      alert(`Failed to login: ${await loginResponse.text()}`)
      return
    }
    const loginData = await loginResponse.json()

    let mockApi = 'ipm-mock.qa-server.dev-qa.ss211208.com'
    if (selectedEnv === 'stage') {
      mockApi = 'mock-ipm-as.ss211208.com'
    }

    const ticketResponse = await fetch(`https://${mockApi}/v1/merchant/${merchantCode}/customer/${playerCode}/ticket`)
    if (!ticketResponse.ok) {
      alert(`Failed to get ticket: ${await ticketResponse.text()}`)
      return
    }
    const ticket = await ticketResponse.text()

    const searchParams = new URLSearchParams()
    if (ticket) {
      searchParams.set('ticket', ticket)
    }
    if (language) {
      searchParams.set('language', language)
    }
    const cashier = document.getElementById('cashier').value
    if (cashier) {
      searchParams.set('cashier', cashier)
    }
    const lobby = document.getElementById('lobby').value
    if (lobby) {
      searchParams.set('lobby', lobby)
    }

    const urlResponse = await fetch(`https://${managementApi}/v1/players/${playerCode}/games/${gameCode}?${searchParams.toString()}`, {
      headers: {
        'Content-Type': 'application/json',
        'x-access-token': loginData.accessToken
      }
    })
    if (!urlResponse.ok) {
      alert(`Failed to get url: ${await urlResponse.text()}`)
      return
    }
    const urlData = await urlResponse.json()

    const url = new URL(urlData.url)
    url.protocol = window.location.protocol
    url.host = window.location.host
    url.port = window.location.port
    url.pathname = `/play/${gameCode}`

    const urlSearchParams = new URLSearchParams(url.hash.split('?')[1])
    urlSearchParams.set('performance', performanceFlag.toString())
    url.search = urlSearchParams.toString()
    url.hash = ''

    localStorage.removeItem('language')
    localStorage.removeItem('lobbyId')
    localStorage.removeItem('playerToken')

    window.location.href = url.href
  })

  document.getElementById('button_ticket').addEventListener('click', async function(e) {
    e.preventDefault()

    const selectedEnv = document.getElementById('env').value
    if (!selectedEnv) {
      alert('Please select a env')
      return
    }
    const playerCode = document.getElementById('playerCode').value
    if (!playerCode) {
      alert('Please playerCode')
      return
    }
    const merchantCode = document.getElementById('merchantCode').value
    if (!merchantCode) {
      alert('Please merchantCode')
      return
    }
    let mockApi = 'ipm-mock.qa-server.dev-qa.ss211208.com'
    if (selectedEnv === 'stage') {
      mockApi = 'mock-ipm-as.ss211208.com'
    }

    const ticketResponse = await fetch(`https://${mockApi}/v1/merchant/${merchantCode}/customer/${playerCode}/ticket`)
    if (!ticketResponse.ok) {
      alert(`Failed to get ticket: ${await ticketResponse.text()}`)
      return
    }
    const ticket = await ticketResponse.text()

    const url = new URL(window.location.href)
    url.pathname = `/ticket/${ticket}`
    
    const urlSearchParams = new URLSearchParams(url.hash.split('?')[1])
    urlSearchParams.set('performance', performanceFlag.toString())
    urlSearchParams.set('language', language)
    urlSearchParams.set('swGameCode', gameCode)
    url.search = urlSearchParams.toString()
    url.hash = ''

    localStorage.removeItem('language')
    localStorage.removeItem('lobbyId')
    localStorage.removeItem('playerToken')

    window.location.href = url.href
  })
</script>
</body>
</html>
