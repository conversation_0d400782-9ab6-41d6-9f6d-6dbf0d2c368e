# EditorConfig is awesome: http://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
indent_size = 2
end_of_line = lf
insert_final_newline = true
max_line_length = 160

# Matches multiple files with brace expansion notation
# Set default charset
[{*.js,*.jsx,*.ts,*.tsx}]
charset = utf-8
indent_style = space

# Matches the exact files either package.json
[package.json]
charset = utf-8
indent_style = space
