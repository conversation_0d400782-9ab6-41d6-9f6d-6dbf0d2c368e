"use strict";

export const handler = async (event, context, callback) => {
  const request = event.Records[0].cf.request;
  const host = request.headers['host'][0].value;

  if (request.uri.startsWith('/api/player/') || request.uri.startsWith('/socket.io/')) {
    request.headers['host'] = [
      {
        key: 'Host',
        value: host.includes('ss211208.com') ? 'player.gcpstg.m27613.com' : 'api-player.yep230616.com'
      }
    ]
  } else if (request.uri.startsWith('/api/terminal/')) {
    request.headers['host'] = [
      {
        key: 'Host',
        value: host.includes('ss211208.com') ? 'terminal.gcpstg.m27613.com' : 'api-terminal.yep230616.com'
      }
    ]
  }

  request.uri = request.uri.replace(/^\/api\/(?:terminal|player)/, '/v1');

  return callback(null, request);
};
