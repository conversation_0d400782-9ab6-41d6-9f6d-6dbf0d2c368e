import cf from 'cloudfront'

const PLAYER_API = 'player-api.qa-server.dev-qa.ss211208.com'

/**
 * @param {AWSCloudFrontFunction.Event} event
 * @returns {Promise<AWSCloudFrontFunction.Request>}
 */
async function handler(event) {
  const request = event.request

  const host = request.headers.host.value
  if (!request.headers.origin) {
    request.headers.origin = { value: `https://${host}` }
  }

  if (request.uri.startsWith('/api/')) {
    cf.updateRequestOrigin({ domainName: PLAYER_API })
  } else {
    if (request.uri.endsWith('/')) {
      request.uri += 'index.html'
    } else if (!request.uri.includes('.')) {
      request.uri += '/index.html'
    }
  }

  return request
}
