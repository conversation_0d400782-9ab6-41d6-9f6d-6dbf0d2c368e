{"version": "1.0", "context": {"eventType": "viewer-request"}, "viewer": {"ip": "0.0.0.0"}, "request": {"method": "GET", "uri": "/blog/index.html", "querystring": {"test": {"value": "true"}, "arg": {"value": "val1", "multivalue": [{"value": "val1"}, {"value": "val2"}]}}, "headers": {"host": {"value": "www.example.com"}, "accept": {"value": "text/html", "multivalue": [{"value": "text/html"}, {"value": "application/xhtml+xml"}]}}, "cookies": {"id": {"value": "CookeIdValue"}, "loggedIn": {"value": "false"}}}}