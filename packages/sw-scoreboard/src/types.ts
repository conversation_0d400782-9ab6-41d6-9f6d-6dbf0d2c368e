import type { AndarBaharPayloadType } from '@/components/AndarBahar/types'
import type { RoulettePayloadType } from './components/Roulette/types'
import type { BaccaratPayloadType } from './components/Baccarat/types'
import type { JokersWheelPayloadType } from '@/components/JokersWheel/types'
import type { GameType } from 'sw-live-core'
import type { SicBoPayloadType } from './components/SicBo/types'

export type SwScoreboardPayloadType =
  RoulettePayloadType
  | BaccaratPayloadType
  | JokersWheelPayloadType
  | AndarBaharPayloadType
  | SicBoPayloadType

export interface ISwScoreboard {
  type: GameType
  payload: SwScoreboardPayloadType
  columnsNumber?: number
  rowsNumber?: number
  itemsCount?: number
  isExpandable?: boolean
  isAtom?: boolean
  className?: string
}
