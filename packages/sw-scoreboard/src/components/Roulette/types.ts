enum Bets {
  straight = 'straight',
  split = 'split',
  street = 'street',
  corner = 'corner',
  line = 'line',
  column = 'column',
  red = 'red',
  black = 'black',
  odd = 'odd',
  even = 'even',
  low = 'low',
  high = 'high',
  dozen = 'dozen'
}
export const BetsTypes = {
  ...Bets,
  zero: '0',
  first: '1-18',
  last: '19-36',
  firstDozen: '1st12',
  secondDozen: '2nd12',
  lastDozen: '3rd12',
  firstColumn: '1stCol',
  secondColumn: '2ndCol',
  lastColumn: '3rdCol',
  neighbours: 'neighbours',
  tier: 'tier',
  orphelins: 'orphelins',
  voisins: 'voisins',
  jeuZero: 'zero',
  raceTrackStraight: 'raceTrackStraight'
}

export type RoulettePayloadType = IRoulettePayloadItem[]

interface IRoulettePayloadItem {
  ballPosition: string
  multipliers?: IRouletteMultiplier[]
}

export interface IRouletteItem {
  ballPosition: string
  multiplier?: number
}

export interface IRouletteMultiplier {
  number: number
  multiplier: number
}

