$scoreboard-items-amount: var(--scoreboard-items-amount);
$scoreboard-item-width: calc(100% / #{$scoreboard-items-amount});

.sw-roulette-scoreboard-item {
  $this: &;

  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  height: 100%;
  width: $scoreboard-item-width;
  box-sizing: border-box;
  color: #fff;
  line-height: 1.15;

  &__number {
    width: 100%;
    height: 100%;
    fill: #fff;
    z-index: 1;
    &_red {
      fill: #c72127;
    }

    &_zero {
      fill: #1a9a48;
    }
  }

  &__multiplier {
    position: absolute;
    bottom: 0;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    height: 100%;
    width: 100%;
    fill: #8ac8ff;
    background-image: linear-gradient(to bottom, rgba(5, 3, 16, 0.8), rgba(29, 112, 190, 0.8));
  }

  &:first-child {
    #{$this}__number {
      fill: #fff;
    }

    &#{$this}_black {
      background-color: #555;
    }
    &#{$this}_red {
      background-color: #bc0014;
    }
    &#{$this}_zero {
      background-color: #009e00;
    }
  }
}
