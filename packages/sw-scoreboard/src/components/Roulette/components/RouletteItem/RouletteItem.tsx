import cn from 'classnames'
import './styles.scss'
import { getBetColorType } from '../../helpers'
import type { IRouletteItem } from '../../types'

export function RouletteItem({ ballPosition, multiplier }: IRouletteItem) {
  const type = ballPosition === '0' ? 'zero' : getBetColorType(ballPosition)

  return (
    <div className={cn('sw-roulette-scoreboard-item', {
      [`sw-roulette-scoreboard-item_${type}`]: type,
    })}
    >
      <svg className={cn('sw-roulette-scoreboard-item__number', {
        [`sw-roulette-scoreboard-item__number_${type}`]: type,
      })}
           viewBox="0 0 100 100"
      >
        <text fontSize="55px" x="50%" y="55%" dominantBaseline="middle" textAnchor="middle">{ballPosition}</text>
      </svg>
      {Boolean(multiplier) && <svg className="sw-roulette-scoreboard-item__multiplier" viewBox="0 0 100 100">
        <text fontSize="30px" x="50%" y="100%" dominantBaseline="middle" textAnchor="middle">{multiplier}x</text>
      </svg>}
    </div>
  )
}
