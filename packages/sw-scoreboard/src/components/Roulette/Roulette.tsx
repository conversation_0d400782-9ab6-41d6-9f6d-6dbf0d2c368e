import { RouletteItem } from './components/RouletteItem/RouletteItem'
import type { IRouletteItem, RoulettePayloadType } from './types'
import { DEFAULT_ROULETTE_ITEMS_COUNT } from '@/const'
import { transformMultipliers } from './helpers'
import { ScoreboardSlider } from '@/components/ScoreboardSlider/Slider'

interface IProps {
  payload: RoulettePayloadType
  isAtom?: boolean
  count?: number
  className?: string
}

export function Roulette({ payload, count = DEFAULT_ROULETTE_ITEMS_COUNT, className, isAtom }: IProps) {
  const items = (Array.isArray(payload) ? payload : []).map<IRouletteItem>(({ ballPosition, multipliers }) => {
    const item: IRouletteItem = {
      ballPosition
    }
    
    if (isAtom) {
      item.multiplier = transformMultipliers(multipliers ?? [], ballPosition)
    }
    
    return item
  }).slice(0, count + 1)
  
  const multipliersKey = payload[0]?.multipliers
    ? payload[0].multipliers.map(m => `${m.number}:${m.multiplier}`).join(',')
    : ''
  const key = `${payload[0]?.ballPosition}-${multipliersKey}-${payload[1]?.ballPosition}`

  return (
    <ScoreboardSlider updateKey={key} className={className}>
      {items.map(({ ballPosition, multiplier }, index) => (
        <RouletteItem key={count - index} ballPosition={ballPosition} multiplier={multiplier}/>
      ))}
    </ScoreboardSlider>
  )
}
