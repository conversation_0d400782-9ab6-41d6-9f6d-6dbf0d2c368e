import { BetsTypes, type IRouletteMultiplier } from './types'

const numberOnTheRacetrack = [
  '0',
  '26',
  '3',
  '35',
  '12',
  '28',
  '7',
  '29',
  '18',
  '22',
  '9',
  '31',
  '14',
  '20',
  '1',
  '33',
  '16',
  '24',
  '5',
  '10',
  '23',
  '8',
  '30',
  '11',
  '36',
  '13',
  '27',
  '6',
  '34',
  '17',
  '25',
  '2',
  '21',
  '4',
  '19',
  '15',
  '32'
]

const numberOnTheRacetrackValueIndex: Record<string, number> = numberOnTheRacetrack.reduce(
  (acc, item, idx) => ({ ...acc, [item]: idx }),
  {}
)

export const getBetColorType = (value: string) => {
  if (value === '0') {
    return BetsTypes.zero
  }
  const idx = numberOnTheRacetrackValueIndex[value]
  return idx % 2 > 0 ? BetsTypes.black : BetsTypes.red
}

export const transformMultipliers = (multipliers: IRouletteMultiplier[], ballPosition: string) => (
  multipliers
    .filter(({ number: luckyNumber }) => ballPosition === luckyNumber.toString())
    .reduce((_, item) => item, {} as IRouletteMultiplier)
    .multiplier
)
