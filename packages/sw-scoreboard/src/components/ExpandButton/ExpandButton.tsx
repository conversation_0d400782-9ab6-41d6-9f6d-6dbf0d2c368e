import type { SyntheticEvent } from 'react'
import { IconCollapse } from './components/IconCollapse/IconCollapse'
import { IconExpand } from './components/IconExpand/IconExpand'
import './styles.scss'

interface IProps {
  isExpanded: boolean
  onClick: () => void
}

export function ExpandButton({ isExpanded, onClick }: IProps) {
  const handleClick = (e: SyntheticEvent) => {
    e.stopPropagation()
    onClick()
  }
  return (
    <div onClick={e => handleClick(e)} className="sw-baccarat-scoreboard-button">
      {isExpanded ? <IconCollapse /> : <IconExpand />}
    </div>
  )
}
