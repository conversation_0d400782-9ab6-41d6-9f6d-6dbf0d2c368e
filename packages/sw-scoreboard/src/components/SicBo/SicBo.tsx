import { useEffect, useState } from 'react'
import { type SicBoPayloadType, SicBoScoreboardType } from './types'
import { ExpandButton } from '../ExpandButton/ExpandButton'
import { SicBoScoreboardArea } from './components/SicBoScoreboardArea/SicBoScoreboardArea'
import './styles.scss'

export interface SicBoScoreboardProps {
  payload: SicBoPayloadType
  isExpandable?: boolean
  className?: string
}

export const SicBo = ({ payload, isExpandable, className }: SicBoScoreboardProps) => {
  const [isExpanded, setExpanded] = useState(false)

  const expandHandler = () => setExpanded(!isExpanded)

  useEffect(() => {
    if (!isExpandable) {
      setExpanded(false)
    }
  }, [isExpandable])

  return (
    <div className={`sw-sic-bo-scoreboard ${className ?? ''}`}>
      {isExpanded ? <SicBoScoreboardArea items={payload} type={SicBoScoreboardType.EvenOdd} /> : null}
      <div className="sw-sic-bo-scoreboard__preview">
        <SicBoScoreboardArea items={payload} type={SicBoScoreboardType.BigSmall} />
        { isExpandable && <ExpandButton isExpanded={isExpanded} onClick={expandHandler} /> }
      </div>
    </div>
  )
}
