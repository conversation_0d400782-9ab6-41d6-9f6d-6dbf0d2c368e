import { useMemo } from 'react'
import type { SicBoPayloadItem, SicBoScoreboardType } from '../../types'
import { convertToGridItems } from '../../helpers'
import { SicBoScoreboardGrid } from '../SicBoScoreboardGrid/SicBoScoreboardGrid'
import { SicBoGridItem } from '../SicBoGridItem/SicBoGridItem'

interface SicBoScoreboardArea {
  items: SicBoPayloadItem[]
  type: SicBoScoreboardType
}

export const SicBoScoreboardArea = ({ type, items }: SicBoScoreboardArea) => {
  const gridItems = useMemo(() => convertToGridItems(items, type), [items])

  return (
    <SicBoScoreboardGrid>
      {gridItems.map(item => (
        <SicBoGridItem key={item.id} {...item} />
      ))}
    </SicBoScoreboardGrid>
  )
}
