import type { SicBoScoreBoardItem } from '../../types'

interface SicBoGridItem extends SicBoScoreBoardItem {
  color?: string
}

export const SicBoGridItem = ({ color, x, y }: SicBoGridItem) => (
  <svg
    className="sw-sic-bo-grid-item"
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    x={x}
    y={y}
    fill="none"
  >
    <circle cx="8" cy="8" r="8" fill={color} />
  </svg>
)
