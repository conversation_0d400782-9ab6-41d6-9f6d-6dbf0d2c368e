export enum DiceType {
  Type1 = '1',
  Type2 = '2',
  Type3 = '3',
  Type4 = '4',
  Type5 = '5',
  Type6 = '6'
}

export interface SicBoScoreBoardItem {
  id: string
  color?: string
  isBig?: boolean
  isOdd?: boolean
  x?: number
  y?: number
}

export interface SicBoPayloadItem {
  roundId: string
  diceResult: DiceType[] | null
}

export enum SicBoScoreboardType {
  BigSmall = 'bigSmall',
  EvenOdd = 'evenOdd'
}

export enum SicBoGridItemType {
  Odd = 'odd',
  Even = 'even',
  Big = 'big',
  Small = 'small'
}

export type SicBoPayloadType = SicBoPayloadItem[]