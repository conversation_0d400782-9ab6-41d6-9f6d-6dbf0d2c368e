import {
  type DiceType,
  type SicBoScoreBoardItem,
  type SicBoPayloadType,
  SicBoGridItemType,
  SicBoScoreboardType,
} from './types'
import { SIC_BO_COLOR_MAP } from './const'

export const stringToNumber = (item: string): number => +item
export const isOdd = (item: number): boolean => Boolean(item % 2)
export const isBig = (item: number) => item > 10

export const getItemTotal = (items: DiceType[] | null | undefined = []) =>
  items?.reduce((acc: number, cur: DiceType) => acc + stringToNumber(cur), 0) ?? 0

export const convertToGridItems = (
  items: SicBoPayloadType,
  sortType: SicBoScoreboardType
): SicBoScoreBoardItem[] => {
  const borderWidth = 1
  const itemWidth = 8
  const rowsAmount = 6
  const maxColsAmount = 42
  const step = borderWidth + itemWidth * 2
  const matrix: SicBoScoreBoardItem[][] = []
  
  // Pre-calculate comparison function
  const compareParam = sortType === SicBoScoreboardType.BigSmall ? isBig : isOdd
  
  // Process items in reverse order (newest first)
  for (let i = items.length - 1; i >= 0; i--) {
    const { roundId, diceResult } = items[i]
    const currentTotal = getItemTotal(diceResult)
    const prev = items[i + 1]
    const prevTotal = getItemTotal(prev?.diceResult)
    
    // Calculate comparison result once
    const currentParam = compareParam(currentTotal)
    const isPreviousEqual = prev ? currentParam === compareParam(prevTotal) : false
    
    // Create item with calculated properties
    const item: SicBoScoreBoardItem = {
      id: roundId || Math.floor(Math.random() * 10000000).toString(),
      ...(sortType === SicBoScoreboardType.BigSmall
        ? { isBig: currentParam }
        : { isOdd: currentParam }
      )
    }
    
    // Add to current column or start new column
    if (isPreviousEqual && matrix.length > 0 && matrix[0].length < rowsAmount) {
      matrix[0].push(item)
    } else {
      // Add new column at the beginning
      matrix.unshift([item])
      
      // Check if we've reached max columns
      if (matrix.length >= maxColsAmount) {
        break
      }
    }
  }
  
  // Convert matrix to result with coordinates
  const result: SicBoScoreBoardItem[] = []
  
  for (let columnIndex = 0; columnIndex < matrix.length; columnIndex++) {
    const columnItems = matrix[columnIndex]
    
    for (let rowIndex = 0; rowIndex < columnItems.length; rowIndex++) {
      const item = columnItems[rowIndex]
      
      result.push({
        ...item,
        color: SIC_BO_COLOR_MAP[sortType === SicBoScoreboardType.BigSmall
          ? (item.isBig ? SicBoGridItemType.Big : SicBoGridItemType.Small)
          : (item.isOdd ? SicBoGridItemType.Odd : SicBoGridItemType.Even)
        ],
        x: columnIndex * step + borderWidth,
        y: rowIndex * step + borderWidth
      })
    }
  }
  
  return result
}

export const getSicBoGridItemType = (
  item: SicBoScoreBoardItem,
  scoreboardType: SicBoScoreboardType
): SicBoGridItemType => {
  const { isBig, isOdd } = item
  if (scoreboardType === SicBoScoreboardType.BigSmall) {
    return isBig ? SicBoGridItemType.Big : SicBoGridItemType.Small
  } else {
    return isOdd ? SicBoGridItemType.Odd : SicBoGridItemType.Even
  }
}
