$scoreboard-items-amount: var(--scoreboard-items-amount);
$scoreboard-item-width: calc(-100% / #{$scoreboard-items-amount});
$history-line-animation-duration: 1s;

.scoreboard-slider {
  width: 100%;
  height: 0;
  padding-top: 14.3853%;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.5);
  user-select: none;

  &__wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  &__container {
    display: flex;
    height: 100%;
    transform: translateX(#{$scoreboard-item-width});
    animation: sw-scoreboard-slider-transform #{$history-line-animation-duration} linear forwards;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

@keyframes sw-scoreboard-slider-transform {
  0% {
    transform: translateX($scoreboard-item-width);
  }
  100% {
    transform: translateX(0);
  }
}
