import './styles.scss'

interface Props {
  className?: string
  updateKey?: string
  children?: React.ReactNode | React.ReactNode[]
  itemsAmount?: number
}

export const ScoreboardSlider = ({ children, className, updateKey, itemsAmount = 9 }: Props) => {
  return (
    <div
      className={`scoreboard-slider ${className ?? ''}`}
      style={{
        '--scoreboard-items-amount': `${itemsAmount}`,
      } as any}
    >
      <div className="scoreboard-slider__wrapper">
        <div className="scoreboard-slider__container" key={updateKey}>
          {children}
        </div>
      </div>
    </div>
  )
}
