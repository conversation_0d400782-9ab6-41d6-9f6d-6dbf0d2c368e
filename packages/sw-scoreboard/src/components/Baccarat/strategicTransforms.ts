import { equals, init, last, length, pathOr } from 'ramda'
import { ColumnType, IHistoryItem, MainBetType, MatrixType } from './types'
import { addNewItemAfterPrediction } from './helpers'


const outcomePath = pathOr(undefined, ['outcome'])

export const compareColumns = (firstRow: ColumnType, secondRow: ColumnType): boolean =>
  equals(length(firstRow), length(secondRow))

export const compareItems = (firstItem: IHistoryItem | undefined, secondItem: IHistoryItem | undefined): boolean =>
  equals(outcomePath(firstItem), outcomePath(secondItem))

export const hasEnoughItems = (columnNumber: number, rowNumber: number, offset: number): boolean =>
  columnNumber >= offset || (columnNumber >= offset - 1 && rowNumber > 0)

const addItem =
  (winner: MainBetType) =>
  (road: MatrixType, isPrediction?: boolean): MatrixType => {
    const newItem: IHistoryItem = { outcome: winner, isTie: false, isPrediction }
    const lastColumn = last(road) || []
    const lastItem = last(lastColumn)
    const isPreviousPrediction = lastItem ? lastItem.isPrediction : false
    const outcome = outcomePath(lastItem)
    const handIsChanged = outcome !== winner

    if (isPreviousPrediction) {
      return addNewItemAfterPrediction(newItem, handIsChanged, road, lastColumn)
    }

    if (outcome === winner) {
      return [...init(road), [...lastColumn, newItem]]
    }

    return [...road, [newItem]]
  }

export const addPlayerItem = addItem(MainBetType.Player)
export const addBankerItem = addItem(MainBetType.Banker)

export const addItemToStrategicRoad = (
  accumulator: MatrixType,
  bigRoadMatrix: MatrixType,
  columnNumber: number,
  rowNumber: number,
  offset: number,
  isPrediction?: boolean
): MatrixType => {
  const road = [...accumulator]

  if (rowNumber === 0) {
    const columnsEqual = compareColumns(bigRoadMatrix[columnNumber - 1], bigRoadMatrix[columnNumber - offset])
    return columnsEqual ? addBankerItem(road, isPrediction) : addPlayerItem(road, isPrediction)
  }
  const columnToTest = bigRoadMatrix[columnNumber - offset + 1]
  const firstItem = columnToTest[rowNumber]
  const secondItem = columnToTest[rowNumber - 1]

  const itemsEqual = compareItems(firstItem, secondItem)

  return itemsEqual ? addBankerItem(road, isPrediction) : addPlayerItem(road, isPrediction)
}

export const strategicTransformer = (
  bigRoadMatrix: MatrixType,
  offset: number,
  matrix: MatrixType = []
): MatrixType => {
  if (matrix.length) {
    const columnNumber = bigRoadMatrix.length - 1
    const lastColumn = last(bigRoadMatrix) || []
    const lastItem = last(lastColumn)
    const isPrediction = lastItem ? lastItem.isPrediction : false
    const rowNumber = lastColumn.length - 1

    return addItemToStrategicRoad(matrix, bigRoadMatrix, columnNumber, rowNumber, offset, isPrediction)
  }

  return bigRoadMatrix.reduce(
    (accumulator: MatrixType, column: ColumnType, columnNumber: number) =>
      column.reduce(
        (result: MatrixType, item: IHistoryItem, rowNumber: number) => {
          const isEnoughData: boolean = hasEnoughItems(columnNumber, rowNumber, offset)

          if (!isEnoughData) {
            return [...result]
          }
          return addItemToStrategicRoad(result, bigRoadMatrix, columnNumber, rowNumber, offset, item.isPrediction)
        },
        [...accumulator]
      ),
    [...matrix]
  )
}
