import type { BaccaratPayloadType, ILobbyPreviewColors } from './types'
import { useEffect, useState } from 'react'
import { DEFAULT_BACCARAT_COLUMNS_NUMBER, DEFAULT_BACCARAT_ROWS_NUMBER } from '@/const'
import { allShoeHistoryPayloadFormatter, transformAllRoads } from './helpers'
import { lobbyPreviewColorsDragonTiger } from './const'
import { lobbyPreviewThemeBaccarat, lobbyPreviewThemeDragonTiger } from './components/Theme/const'
import { BaccaratExpanded } from './components/BaccaratExpanded/BaccaratExpanded'
import { BigRoad } from './components/BigRoad/BigRoad'
import { ExpandButton } from '../ExpandButton/ExpandButton'
import './styles.scss'
import { ThemeProvider } from '@/components/Baccarat/components/Theme/Theme'
import { GameType } from 'sw-live-core'

interface IProps {
  type: GameType
  payload: BaccaratPayloadType
  isExpandable?: boolean
  columns?: number
  rows?: number
  className?: string
}
export function Baccarat({ payload, columns = DEFAULT_BACCARAT_COLUMNS_NUMBER, rows = DEFAULT_BACCARAT_ROWS_NUMBER, type, isExpandable = true, className}: IProps) {
  const [isExpanded, setExpanded] = useState(false)

  const isDragonTiger = type === GameType.DRAGON_TIGER
  const lobbyPreviewColors: ILobbyPreviewColors | undefined = isDragonTiger ? lobbyPreviewColorsDragonTiger : undefined
  const theme = isDragonTiger ? lobbyPreviewThemeDragonTiger : lobbyPreviewThemeBaccarat

  const shoesData = payload ? allShoeHistoryPayloadFormatter([[], [], payload]) : null
  const currentShoeData = transformAllRoads(shoesData?.currentShoeHistory ?? [])
  const { beadRoadData, bigRoadData, smallRoadData, cockroachRoadData } = currentShoeData.roadsData

  const expandHandler = () => setExpanded(!isExpanded)

  useEffect(() => {
    if (!isExpandable) {
      setExpanded(false)
    }
  }, [isExpandable])

  return (
    <ThemeProvider theme={theme}>
      <div className={`sw-baccarat-scoreboard ${className ?? ''}`}>
        {isExpanded && (
          <BaccaratExpanded
            beadRoadData={beadRoadData}
            smallRoadData={smallRoadData}
            cockroachRoadData={cockroachRoadData}
            columns={columns}
            rows={rows}
            lobbyPreviewColors={lobbyPreviewColors}
          />
        )}
        <div className="sw-baccarat-scoreboard__big">
          <BigRoad data={bigRoadData} columns={columns} lobbyPreviewColors={lobbyPreviewColors} />
          { isExpandable && <ExpandButton isExpanded={isExpanded} onClick={expandHandler} /> }
        </div>
      </div>
    </ThemeProvider>
  )
}
