import { ComponentType } from 'react'

export type BaccaratPayloadType = IBaccaratPayloadItem[]

export enum MainBetType {
  Player = 'player',
  Tie = 'tie',
  Banker = 'banker'
}

export interface ILobbyPreviewColors {
  player: string
  banker: string
  tie: string
}

export interface IHistoryItem {
  outcome?: MainBetType
  isTie?: boolean
  point?: number
  isPrediction?: boolean
  isBankerPair?: boolean
  isPlayerPair?: boolean
  tiesNumber?: number
}

export type HistoryArrayType = Array<IHistoryItem | typeof undefined>

export interface IBaccaratPayloadItem {
  outcome: MainBetType
  points: IPoints
  playerPair: boolean
  bankerPair: boolean
}

export interface IPoints {
  playerPoints: number
  bankerPoints: number
}

export interface IShoesData {
  totalShoeHistory: IHistoryItem[]
  currentShoeHistory: IHistoryItem[]
  lastShoeHistory: IHistoryItem[]
  preLastShoeHistory: IHistoryItem[]
}


export interface IGrid extends IBasicGrid, ISize {
  type: ComponentType<ILineItem> | ComponentType<ICircleItem> | ComponentType<IBeadCircleItem> | null
  onClick?: () => void
  circleFont?: IFont
  matrix?: MatrixType
  theme?: any
  cursor?: string
  wrapToNextRow?: boolean
  factor?: number
  tieTextFont?: IFont
  symbol?: BeadRoadSymbol
  themeColors?: IThemeColors
  className?: string
  lobbyPreviewColors?: ILobbyPreviewColors
  satellites?: ICircleSatellites
}

interface ICircleSatellites {
  radius: number
  playerPairCoordinates: number[]
  bankerPairCoordinates: number[]
}

export interface IBeadCircleItem extends ICircleItem {
  satellites?: ICircleSatellites
}

export interface IThemeColors {
  fillRect: string
  lineSroke: {
    color: string
  }
  roadSeparator: {
    color: string
    width: number
  }
  textColor: string
  rectStroke: number
}


export interface IBasicGrid extends IElementSizes, IBoardSettings, IBoardChildren {
  start: SizeType
  radius?: number
}

export type PositionType = [number, number]
export type SizeType = number | PositionType

interface ISize {
  width?: number
  height?: number
}

export interface ILineItem extends IBasicCellItem {
  radius?: number
}

export interface IBasicCellItem extends IElementSizes, IBoardSettings {
  start?: SizeType
  children: IHistoryItem
  dotSize?: number
  theme?: any
}

export interface ICircleItem extends IBasicCellItem {
  radius: number
  tieTextFont?: IFont
  symbol?: BeadRoadSymbol
  lobbyPreviewColors?: ILobbyPreviewColors
}

export enum BeadRoadSymbol {
  number = 'number',
  letter = 'letter',
  cn = 'cn'
}

export interface IElementSizes {
  col: number
  row: number
  size: number
}

export interface IBoardSettings {
  cellStroke?: IStroke
  stroke?: IStroke
  font?: IFont
}

export interface IStroke {
  color?: string
  strokeWidth?: number
  strokeOpacity?: number
}

export interface IFont {
  size?: number
  weight?: string
}

export interface IText {
  value: string
  position: PositionType
}


interface IBoardChildren {
  children?: HistoryArrayType
  text?: IText
}

export type MatrixType = ColumnType[]
export type ColumnType = IHistoryItem[]

export interface ITheme {
  betsArea: IThemeBetsArea
  scoreboard: IScoreboard
}

export interface IThemeBetsArea {
  background: string
  backgroundOpacity: number
  backgroundNormal: string
  backgroundDisabled: string
  backgroundWin: string
  backgroundWinOpacity: number
  bgBettingMode: string
  bgBettingModeOpacity: number
  betModeAnimationBackground: IBetModeAnimationBackground
  bgBettingModeAnimationSide: string
  backgroundPointPlayer: string
  backgroundPointBanker: string
  backgroundMenu: string
  foreground: string
  scoreboardStroke: string
  scoreboardCellStroke: string
  scoreboardTextColor: string
  textColorGreen: string
  textColorRed: string
  textColorBlue: string
  textColorDisabled: string
  betColor: string
  bankerColor: string
  playerColor: string
  tieColor: string
  scorePoints: IKeyValue<string>
  winPlayerMessageColor: string
  winBankerMessageColor: string
  winTieMessageColor: string
  playerProgressBarColor: string
  bankerProgressBarColor: string
  tieProgressBarColor: string
}

export interface IBetModeAnimationBackground {
  player: string
  banker: string
  tie: string
  playerPair?: string
  bankerPair?: string

  playerOdd?: string
  playerEven?: string
  playerBig?: string
  playerSmall?: string
  bankerOdd?: string
  bankerEven?: string
  bankerBig?: string
  bankerSmall?: string
}

export interface IKeyValue<ValueType> {
  [key: string]: ValueType;
}

export interface IMatrixes {
  bigRoadMatrix: MatrixType
  cockroachRoadMatrix: MatrixType
  smallRoadMatrix: MatrixType
  bigEyeRoadMatrix: MatrixType
}

export interface IRoads {
  bigRoadData: HistoryArrayType
  beadRoadData: HistoryArrayType
  bigEyeRoadData: HistoryArrayType
  smallRoadData: HistoryArrayType
  cockroachRoadData: HistoryArrayType
}
export type RoadDataType = Array<IHistoryItem | undefined>

export interface IScoreboard {
  latinSymbols: IScoreboardSymbols
  chineseSymbols: IScoreboardSymbols
  transformRank?: (point: number) => string
}

interface IScoreboardSymbols {
  banker: string
  player: string
  tie: string
}
