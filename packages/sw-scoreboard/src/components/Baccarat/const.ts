import { ILobbyPreviewColors, IThemeColors } from './types'
import { DEFAULT_BACCARAT_ROWS_NUMBER } from '../../const'

export const columnSize = [20.52, 124]

export const gridOptions = {
  size: 20.5,
  start: 0,
  row: DEFAULT_BACCARAT_ROWS_NUMBER,
  radius: 9,
  cellStroke: { strokeWidth: 3 }
}

export const lightTheme: IThemeColors = {
  fillRect: '#fff',
  lineSroke: {
    color: '#ddd'
  },
  roadSeparator: {
    color: '#858585',
    width: 1
  },
  textColor: '#5A5A5A',
  rectStroke: 1
}

export const lobbyPreviewColorsDragonTiger: ILobbyPreviewColors = {
  player: '#ec2033',
  banker: '#f4a400',
  tie: '#09bd04'
}
