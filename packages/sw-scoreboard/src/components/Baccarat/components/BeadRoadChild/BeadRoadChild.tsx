import { isNil } from 'ramda'
import React, { ComponentType, useContext } from 'react'
import { BeadRoadSymbol, IBeadCircleItem, IScoreboard, MainBetType } from '../../types'
import { RoadItem } from '../../components/Roaditem/BaccaratRoadItem'
import { Circle } from '../../components/SVG/Circle'
import { ChildHOC } from '../../ChildHOC'
import { Text } from '../SVG/Text'
import { CoordinateType } from '../../components/SVG/types'
import { ThemeContext } from '../../components/Theme/Theme'

const stroke = { color: '#fff', strokeWidth: 1 }

const getSymbol = (symbol: BeadRoadSymbol, { outcome, point }: { outcome: MainBetType, point: number}, scoreboard: IScoreboard): string => {
  const { latinSymbols, chineseSymbols, transformRank } = scoreboard
  switch (symbol) {
    case BeadRoadSymbol.letter:
      return latinSymbols[outcome]
    case BeadRoadSymbol.cn:
      return chineseSymbols[outcome]
    default:
      return typeof transformRank === 'function' ? transformRank(point) : String(point)
  }
}

export const BeadRoadChildComponent: ComponentType<IBeadCircleItem> = ({
                                                                         size,
                                                                         radius,
                                                                         font = { size: 10, weight: 'bold' },
                                                                         children: { outcome, point, isPrediction, isBankerPair, isPlayerPair },
                                                                         symbol,
                                                                         satellites = {
                                                                           radius: 3,
                                                                           playerPairCoordinates: [5, 4],
                                                                           bankerPairCoordinates: [16, 16]
                                                                         }
                                                                       }: IBeadCircleItem) => {
  const center = size / 2
  const fontSymbol = symbol === BeadRoadSymbol.cn ? { ...font, size: 12 } : font
  const { theme: { betsArea, scoreboard} } = useContext(ThemeContext)

  const { radius: satelliteRadius, playerPairCoordinates, bankerPairCoordinates } = satellites
  return (
    <RoadItem
      size={size}
      radius={radius}
      outcome={outcome}
      fill={outcome ? betsArea[`${outcome}Color`] : ''}
      isPrediction={isPrediction}
    >
      {!isNil(point) && (
        <Text start={[center, center - 1]} color={"#fff"} font={fontSymbol} dy={(font?.size || 0) / 2.2}>
          {getSymbol(symbol as BeadRoadSymbol, { outcome, point } as { outcome: MainBetType, point: number}, scoreboard)}
        </Text>
      )}
      {isPlayerPair && (
        <Circle stroke={stroke} center={playerPairCoordinates as CoordinateType} fill={betsArea.playerColor} radius={satelliteRadius} />
      )}
      {isBankerPair && (
        <Circle stroke={stroke} center={bankerPairCoordinates as CoordinateType} fill={betsArea.bankerColor} radius={satelliteRadius} />
      )}
    </RoadItem>
  )
}

export const BeadRoadChild = ChildHOC(BeadRoadChildComponent)
