import React, { ComponentType, useContext } from 'react'
import { ChildHOC } from '../../ChildHOC'
import { RoadItem } from '../Roaditem/BaccaratRoadItem'
import { ICircleItem } from '../../types'
import { ThemeContext } from '../../components/Theme/Theme'


export const SmallRoadChildComponent: ComponentType<ICircleItem> = ({
                                                                      size,
                                                                      radius,
                                                                      children: { outcome, isPrediction }
                                                                    }: ICircleItem) => {
  const { theme: { betsArea} } = useContext(ThemeContext)

  return (
    <RoadItem
      radius={radius}
      outcome={outcome}
      fill={outcome ? betsArea[`${outcome}Color`] : ''}
      size={size}
      isPrediction={isPrediction}
    />
  )
}

export const SmallRoadChild = ChildHOC(SmallRoadChildComponent)
