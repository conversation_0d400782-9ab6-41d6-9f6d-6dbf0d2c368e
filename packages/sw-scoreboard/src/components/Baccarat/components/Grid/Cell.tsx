import React, { ReactNode } from 'react'
import { Group } from '../SVG/Group'
import { Rect } from '../SVG/Rect'
import { IThemeColors } from '../../types'

interface IProps {
  col: number
  row: number
  size: number
  children?: ReactNode
  borderWidth: number
  themeColors?: IThemeColors
}

export const Cell = ({ col, row, size, children, borderWidth, themeColors }: IProps) => {
  const borderMargin = borderWidth / 2
  const stroke = { color: themeColors?.lineSroke.color }

  return (
    <Group start={[col * size + borderMargin, row * size + borderMargin]}>
      <Rect fill={themeColors?.fillRect} stroke={stroke} size={size} />
      {children}
    </Group>
  )
}

