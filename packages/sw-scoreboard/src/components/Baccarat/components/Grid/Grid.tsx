import { IGrid, IHistoryItem, SizeType } from '../../types'
import { Group } from '../SVG/Group'
import { Rect } from '../SVG/Rect'
import { Text } from '../SVG/Text'
import { Cell } from '../Grid/Cell'
import { range, slice } from 'ramda'

export const Grid = ({
                         size,
                         col,
                         row,
                         type,
                         cellStroke,
                         stroke = { strokeWidth: 1 },
                         radius = 0,
                         start,
                         font = { size: 16 },
                         children,
                         text,
                         circleFont = { size: 14, weight: '700' },
                         tieTextFont = { size: 16 },
                         onClick,
                         symbol,
                         themeColors,
                         className,
                         lobbyPreviewColors,
                         satellites
                       }: IGrid) => {
  const CellItem: any = type
  const { strokeWidth = 1 } = stroke
  const BorderSizes: [number, number] = [size * col + strokeWidth, size * row + strokeWidth]
  const currentColumnsCount = children ? Math.ceil(children.length / row) : 0
  const delta = currentColumnsCount - col
  const roadData = delta > 0 && children ? slice(delta * row, children.length, children) : children
  const rectStroke = { strokeWidth: themeColors?.rectStroke, color: themeColors?.lineSroke.color }

  return (
    <Group onClick={onClick} start={start} className={className}>
      <Rect start={0} size={BorderSizes} stroke={rectStroke} fill="transparent" />
      {range(0, col).map((colNumber: number) =>
        range(0, row).map((rowNumber: number) => {
          const cellNum = colNumber * row + rowNumber
          const cellItemChild: IHistoryItem | undefined = roadData && roadData[cellNum]

          return (
            <Cell
              borderWidth={strokeWidth}
              key={cellNum}
              col={colNumber}
              row={rowNumber}
              size={size}
              themeColors={themeColors}
            >
              {cellItemChild ? (
                <CellItem
                  stroke={cellStroke}
                  col={colNumber}
                  row={rowNumber}
                  size={size}
                  radius={radius}
                  font={circleFont}
                  cellStroke={cellStroke}
                  tieTextFont={tieTextFont}
                  symbol={symbol}
                  lobbyPreviewColors={lobbyPreviewColors}
                  satellites={satellites}
                >
                  {cellItemChild}
                </CellItem>
              ) : null}
            </Cell>
          )
        })
      )}
      {text && (
        <Text start={text.position as SizeType} color={themeColors?.textColor} font={font}>
          {text.value}
        </Text>
      )}
    </Group>
  )
}
