import { Circle } from '../SVG/Circle'
import cn from 'classnames'
import { IStroke } from '../../types'
import { ReactNode } from 'react'

interface IProps {
  size: number
  isPrediction?: boolean
  fill?: string
  children?: ReactNode
  stroke?: IStroke
  radius: number
  outcome?: string
}

export const RoadItem = ({ size, isPrediction, fill, children, stroke, radius, outcome }: IProps) => (
  <>
    <Circle
      className={cn({ [`circle-${outcome}`]: outcome })}
      radius={radius}
      center={size / 2}
      stroke={stroke}
      fill={fill}
      blinking={isPrediction}
    />
    {children}
  </>
)
