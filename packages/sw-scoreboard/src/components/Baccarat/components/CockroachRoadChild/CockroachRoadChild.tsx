import React, { ComponentType, useContext } from 'react'
import { ILineItem } from '../../types'
import { Line } from '../../components/SVG/Line'
import { ChildHOC } from '../../ChildHOC'
import { ThemeContext } from '../../components/Theme/Theme'


export const CockroachRoadChildComponent: ComponentType<ILineItem> = ({
                                                                        size,
                                                                        cellStroke,
                                                                        children: { outcome, isPrediction },
                                                                      }: ILineItem) => {
  const { theme: { betsArea } } = useContext(ThemeContext)

  const lineStroke = {
    ...cellStroke,
    color: outcome ? betsArea[`${outcome}Color`] : ''
  }
  const offset = (lineStroke.strokeWidth || 0) / 2
  const className = outcome ? `line-${outcome}` : ''

  return <Line className={className} start={offset} end={size - offset} stroke={lineStroke} blinking={isPrediction} />
}

export const CockroachRoadChild = ChildHOC(CockroachRoadChildComponent)
