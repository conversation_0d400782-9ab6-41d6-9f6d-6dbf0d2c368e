import { createContext, ReactNode } from 'react'
import { ITheme } from '@/components/Baccarat/types'
import { lobbyPreviewThemeBaccarat } from '@/components/Baccarat/components/Theme/const'

export const ThemeContext = createContext({
  theme: lobbyPreviewThemeBaccarat
});

const { Provider } = ThemeContext;
interface IProps {
  theme: ITheme
  children: ReactNode
}

export const ThemeProvider = ({ theme, children }: IProps) => {
  return <Provider value={{theme}}>{children}</Provider>
}
