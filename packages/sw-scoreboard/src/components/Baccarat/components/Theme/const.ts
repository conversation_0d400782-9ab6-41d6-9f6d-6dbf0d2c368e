import { IKeyValue, ITheme } from '@/components/Baccarat/types'

export const betBackgroundColors: IKeyValue<string> = {
  player: '#b01726',
  banker: '#e38800',
  tie: '#289525'
}

export const dragonTigerScorePoints: { [key: number]: string }= {
  0: '0',
  1: 'A',
  2: '2',
  3: '3',
  4: '4',
  5: '5',
  6: '6',
  7: '7',
  8: '8',
  9: '9',
  10: '10',
  11: 'J',
  12: 'Q',
  13: 'K'
}
export const scorePoints = {
  0: '0',
  1: '1',
  2: '2',
  3: '3',
  4: '4',
  5: '5',
  6: '6',
  7: '7',
  8: '8',
  9: '9'
}

export const lobbyPreviewThemeBaccarat: ITheme = {
  betsArea: {
    background: '#262626',
    backgroundOpacity: 1,
    backgroundNormal: '#262626',
    backgroundDisabled: '#1a1a1a',
    backgroundWin: '#838383',
    backgroundWinOpacity: 0.7,
    bgBettingMode: '#3e3e3e',
    bgBettingModeOpacity: 1,
    betModeAnimationBackground: {
      player: '#1b76ee',
      banker: '#cd2a39',
      tie: '#289525',
      playerPair: '#1a66ca',
      bankerPair: '#ac1e2b'
    },
    bgBettingModeAnimationSide: '#707070',
    backgroundPointPlayer: '#1b76ee',
    backgroundPointBanker: '#ec2033',
    backgroundMenu: '#1a1a1a',
    foreground: '#fff',
    textColorGreen: '#0aa706',
    textColorRed: '#ec2033',
    textColorBlue: '#1b76ee',
    textColorDisabled: '',
    scoreboardStroke: '#dddddd',
    scoreboardCellStroke: '#dddddd',
    scoreboardTextColor: '#656565',
    betColor: '',
    scorePoints,
    playerColor: '#1b76ee',
    bankerColor: '#ec2033',
    tieColor: '#0aa706',
    winPlayerMessageColor: '#1b76ee',
    winBankerMessageColor: '#ec2033',
    winTieMessageColor: '#09bd04',
    playerProgressBarColor: 'rgba(27, 118, 238, 0.3)',
    bankerProgressBarColor: 'rgba(236, 32, 51, 0.3)',
    tieProgressBarColor: 'rgba(10, 167, 6, 0.3)'
  },
  scoreboard: {
    latinSymbols: {
      banker: 'B',
      player: 'P',
      tie: 'T'
    },
    chineseSymbols: {
      banker: '庄',
      player: '闲',
      tie: '和'
    }
  }
}

export const lobbyPreviewThemeDragonTiger: ITheme = {
  betsArea: {
    background: '#323232',
    backgroundOpacity: 0.85,
    backgroundNormal: '#262626',
    backgroundDisabled: '#1a1a1a',
    backgroundWin: '#989898',
    backgroundWinOpacity: 0.6,
    bgBettingMode: '#323232',
    bgBettingModeOpacity: 0.85,
    betModeAnimationBackground: {
      player: betBackgroundColors.player,
      banker: betBackgroundColors.banker,
      tie: betBackgroundColors.tie,
      playerOdd: betBackgroundColors.player,
      playerEven: betBackgroundColors.player,
      playerBig: betBackgroundColors.player,
      playerSmall: betBackgroundColors.player,
      bankerOdd: betBackgroundColors.banker,
      bankerEven: betBackgroundColors.banker,
      bankerBig: betBackgroundColors.banker,
      bankerSmall: betBackgroundColors.banker
    },
    bgBettingModeAnimationSide: '#707070',
    backgroundPointPlayer: '#ec2033',
    backgroundPointBanker: '#f4a400',
    backgroundMenu: '#1a1a1a',
    foreground: '#fff',
    textColorGreen: '#0aa706',
    textColorRed: '#ec2033',
    textColorBlue: '#1b76ee',
    textColorDisabled: '',
    scoreboardStroke: '#dddddd',
    scoreboardCellStroke: '#dddddd',
    scoreboardTextColor: '#656565',
    betColor: '',
    scorePoints: dragonTigerScorePoints,
    playerColor: '#ec2033',
    bankerColor: '#f4a400',
    tieColor: '#09bd04',
    winPlayerMessageColor: '#ec2033',
    winBankerMessageColor: '#f4a400',
    winTieMessageColor: '#09bd04',
    playerProgressBarColor: 'rgba(236, 32, 51, 0.3)',
    bankerProgressBarColor: 'rgba(244, 164, 0, 0.3)',
    tieProgressBarColor: 'rgba(9, 189, 4, 0.3)'
  },
  scoreboard: {
    latinSymbols: {
      banker: 'T',
      player: 'D',
      tie: 'T'
    },
    chineseSymbols: {
      banker: '虎',
      player: '龙',
      tie: '和'
    },
    transformRank: (point: number) => dragonTigerScorePoints[point]
  }
}

