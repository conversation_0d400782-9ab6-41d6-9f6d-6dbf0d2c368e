import type { FunctionComponent } from 'react'
import { columnSize, gridOptions, lightTheme } from '../../const'
import type { HistoryArrayType, IGrid, ILobbyPreviewColors } from '../../types'
import { Svg } from '../SVG/Svg'
import { Grid } from '../../components/Grid/Grid'
import { CockroachRoadChild } from '../../components/CockroachRoadChild/CockroachRoadChild'
import { SmallRoadChild } from '../../components/SmallRoadChild/SmallRoadChild'
import { BeadRoadChild } from '../../components/BeadRoadChild/BeadRoadChild'


interface IProps {
  smallRoadData: HistoryArrayType
  cockroachRoadData: HistoryArrayType
  beadRoadData: HistoryArrayType
  columns: number
  rows: number
  lobbyPreviewColors?: ILobbyPreviewColors
}

export const BaccaratExpanded: FunctionComponent<IProps> = (props: IProps) => {
  const [columnWidth, columnHeight] = columnSize
  const { beadRoadData, columns, lobbyPreviewColors, smallRoadData, cockroachRoadData } = props
  const width = columnWidth * columns

  const baseProps: IGrid = {
    ...gridOptions,
    type: null,
    col: columns,
    lobbyPreviewColors,
    themeColors: lightTheme
  }

  const cockroachRoadGridProps: IGrid = {
    ...baseProps,
    type: CockroachRoadChild
  }

  const smallRoadGridProps: IGrid = {
    ...baseProps,
    type: SmallRoadChild
  }

  const beadRoadGridProps: IGrid = {
    ...baseProps,
    type: BeadRoadChild,
    size: gridOptions.size * 2,
    radius: gridOptions.radius * 2,
    col: columns / 2,
    row: 4,
    font: { size: 32 },
    tieTextFont: { size: 32 },
    circleFont: { size: 28, weight: '700' },
    satellites: {
      radius: 6,
      bankerPairCoordinates: [10, 8],
      playerPairCoordinates: [32, 32]
    }
  }

  const gapHeight = 2
  const gridHeight = columnHeight + gapHeight
  const beadRoadHeight = columnHeight + columnWidth * 2 + gapHeight

  return (
    <>
      <Svg width={width} height={gridHeight}>
        <Grid {...cockroachRoadGridProps}>{cockroachRoadData}</Grid>
      </Svg>
      <Svg width={width} height={gridHeight}>
        <Grid {...smallRoadGridProps}>{smallRoadData}</Grid>
      </Svg>
      <Svg width={width} height={beadRoadHeight}>
        <Grid {...beadRoadGridProps}>{beadRoadData}</Grid>
      </Svg>
    </>
  )
}
