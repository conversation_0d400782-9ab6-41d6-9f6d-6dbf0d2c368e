import { ComponentType, useContext } from 'react'
import { ICircleItem, IStroke, MainBetType } from '../../types'
import { ChildHOC } from '../../ChildHOC'
import { Text } from '../SVG/Text'
import { Line } from '../SVG/Line'
import { RoadItem } from '../Roaditem/BaccaratRoadItem'
import { ThemeContext } from '@/components/Baccarat/components/Theme/Theme'

export const BigRoadChildComponent: ComponentType<ICircleItem> = ({
                                                                    size,
                                                                    radius,
                                                                    cellStroke,
                                                                    children: { outcome, isTie, isPrediction, tiesNumber },
                                                                    tieTextFont,
                                                                    lobbyPreviewColors
                                                                  }: ICircleItem) => {
  const { theme: { betsArea } } = useContext(ThemeContext)

  const isCircleNeeded = outcome !== MainBetType.Tie
  const childStroke: IStroke = isCircleNeeded
    ? {
      ...cellStroke,
      color: outcome && betsArea ? !lobbyPreviewColors ? betsArea[`${outcome}Color`] : lobbyPreviewColors[outcome] : undefined
    }
    : { ...cellStroke }
  const lineStroke = {
    ...cellStroke,
    color: betsArea.tieColor
  }

  return (
    <RoadItem size={size} radius={radius} outcome={outcome} stroke={childStroke} isPrediction={isPrediction}>
      {isTie && (
        <>
          <Line start={1.7} end={size - 1.7} stroke={lineStroke} className="line-tie" />
          {tiesNumber && tiesNumber > 1 && (
            <Text color="#000" start={[size / 2, size / 2]} font={tieTextFont}>
              {tiesNumber}
            </Text>
          )}
        </>
      )}
    </RoadItem>
  )
}

export const BigRoadChild = ChildHOC(BigRoadChildComponent)
