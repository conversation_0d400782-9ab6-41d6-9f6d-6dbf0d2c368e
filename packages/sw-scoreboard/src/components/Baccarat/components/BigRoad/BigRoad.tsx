import { HistoryArrayType, IGrid, ILobbyPreviewColors } from '../../types'
import { columnSize, gridOptions, lightTheme } from '../../const'
import { BigRoadChild } from '../BigRoadChild/BigRoadChild'
import { Svg } from '../../components/SVG/Svg'
import { Grid } from '../Grid/Grid'

interface IBaccaratBigRoad {
  data: HistoryArrayType
  columns: number
  lobbyPreviewColors?: ILobbyPreviewColors
}

export function BigRoad({ data, columns, lobbyPreviewColors }: IBaccaratBigRoad) {
  const [columnWidth, columnHeight] = columnSize
  const width = columns * columnWidth
  const gridProps: IGrid = {
    ...gridOptions,
    col: columns,
    type: BigRoadChild
  }
  return (
    <Svg width={width} height={columnHeight}>
      <Grid {...gridProps} lobbyPreviewColors={lobbyPreviewColors} themeColors={lightTheme}>
        {data}
      </Grid>
    </Svg>
  )
}
