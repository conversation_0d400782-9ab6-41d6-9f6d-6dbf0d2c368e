export type CoordinateType = [number, number] | number

export interface IBaseLine {
  textAnchor: string
  alignmentBaseline: 'auto' | 'baseline' | 'before-edge' | 'text-before-edge' | 'middle' | 'central' | 'after-edge'
}

export type TransformType = [number, number, number, number]

export interface IAnimation {
  animation?: {
    attributeName: string
    from: string | number
    to: string | number
    dur: string | number
    repeatCount: 'indefinite' | number
    values: string
    keyTimes: string
  }
}

export interface IRounding {
  xRounding: number
  yRounding: number
}
