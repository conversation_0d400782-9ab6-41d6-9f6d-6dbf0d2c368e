import React from 'react'

import { CoordinateType, IBaseLine, TransformType } from './types'
import { parseCoord } from './helpers'
import { IFont } from '../../types'

export interface IText {
  start: CoordinateType
  color?: string
  font?: IFont
  children: string | number
  baseLine?: IBaseLine
  cursor?: string
  transform?: TransformType
  fillOpacity?: number
  dy?: number
}

export const Text = ({
  baseLine = { textAnchor: 'middle', alignmentBaseline: 'central' },
  start,
  color,
  font,
  children,
  cursor,
  transform = [1, 0, 0, 1],
  fillOpacity = 1,
  dy
}: IText) => {
  const [left, top]: [number, number] = parseCoord(start)
  const { textAnchor, alignmentBaseline } = baseLine
  const currentTransform = `matrix(${transform[0]} ${transform[1]} ${transform[2]} ${transform[3]} ${left} ${top})`
  const aligmentProps = typeof dy !== 'undefined' ? { dy } : { alignmentBaseline, dominantBaseline: alignmentBaseline }

  return (
    <text
      transform={currentTransform}
      fontWeight={font?.weight}
      cursor={cursor}
      fill={color}
      fontSize={font?.size}
      textAnchor={textAnchor}
      fillOpacity={fillOpacity}
      {...aligmentProps}
    >
      {children}
    </text>
  )
}
