import React, { ReactNode } from 'react'

import { CoordinateType } from './types'

export interface IProps {
  width: number
  height: number
  children: ReactNode | ReactNode[]
  className?: string
  start?: CoordinateType
}

export const Svg = ({ width, height, children, className, start = [0, 0] }: IProps) => {
  const [left, top] = Array.isArray(start) ? start : [0, 0]
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox={`${left} ${top} ${width} ${height}`} className={className}>
      {children}
    </svg>
  )
}
