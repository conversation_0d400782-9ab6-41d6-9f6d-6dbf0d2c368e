import React from 'react'
import { CoordinateType, IAnimation } from './types'
import { IStroke } from '../../types'
import { parseCoord } from '../SVG/helpers'

interface IProps extends IAnimation {
  stroke: IStroke
  start: CoordinateType
  end: CoordinateType
  blinking?: boolean
  className?: string
}

export const Line = ({ stroke, start, end, blinking, animation, className }: IProps) => {
  const [left, top] = parseCoord(end)
  const [right, bottom] = parseCoord(start)
  const { color, strokeWidth } = stroke

  return (
    <line className={className} x1={right} y1={top} x2={left} y2={bottom} strokeWidth={strokeWidth} stroke={color}>
      {blinking && (
        <animate attributeType="CSS" attributeName="opacity" from="1" to="0" dur="1s" repeatCount="indefinite" />
      )}
      {animation && <animate attributeType="CSS" {...animation} />}
    </line>
  )
}
