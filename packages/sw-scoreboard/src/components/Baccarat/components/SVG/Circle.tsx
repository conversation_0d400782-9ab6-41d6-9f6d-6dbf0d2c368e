import React from 'react'
import { CoordinateType, IAnimation } from './types'
import { IStroke } from '../../types'
import { parseCoord } from './helpers'

interface IProps extends IAnimation {
  stroke?: IStroke
  fill?: string
  radius: number
  center: CoordinateType
  blinking?: boolean
  cursor?: string
  filter?: string
  className?: string
}

export const Circle = ({
                         className,
                         stroke = {},
                         fill = 'transparent',
                         radius,
                         center,
                         blinking,
                         animation,
                         cursor,
                         filter
                       }: IProps) => {
  const [x, y] = parseCoord(center)
  const { strokeWidth, color } = stroke

  return (
    <circle
      className={className}
      cursor={cursor}
      r={radius}
      cx={x}
      cy={y}
      fill={fill}
      stroke={color}
      strokeWidth={strokeWidth}
      filter={filter}
    >
      {blinking && (
        <animate attributeType="CSS" attributeName="opacity" from="1" to="0" dur="1s" repeatCount="indefinite" />
      )}
      {animation && <animate attributeType="CSS" {...animation} />}
    </circle>
  )
}
