import { CoordinateType } from './types'
import { ReactNode } from 'react'
import { IKeyValue } from '../../types'
import { parseCoord } from './helpers'

export interface IGroup {
  children?: (ReactNode | undefined)[] | ReactNode
  start?: CoordinateType
  cursor?: string
  onClick?: () => void
  onMouseEnter?: () => void
  onMouseLeave?: () => void
  transform?: {
    start?: CoordinateType
    rotate?: number
    base?: CoordinateType
  }
  className?: string
  dataAttributes?: IKeyValue<string>
  fill?: string
  opacity?: number
  stroke?: string
  filter?: string
}

export const Group = ({
  children,
  start,
  cursor,
  onClick,
  transform = { rotate: 0, start: 0, base: 0 },
  onMouseEnter,
  onMouseLeave,
  className,
  dataAttributes,
  ...restProps
}: IGroup) => {
  const { base } = transform
  const [left = 0, top = 0] = start ? parseCoord(start) : parseCoord(transform.start || 0)
  const [baseLeft, baseTop] = base ? parseCoord(base) : [0, 0]

  return (
    <g
      className={className}
      cursor={cursor}
      transform={`translate(${left} ${top}) rotate(${transform.rotate} ${baseLeft} ${baseTop})`}
      {...dataAttributes}
      onMouseLeave={onMouseLeave}
      onMouseEnter={onMouseEnter}
      onMouseDown={onClick}
      onTouchEnd={(e) => {
        if (!onClick) {
          return
        }
        e.preventDefault()
        onClick()
      }}
      {...restProps}
    >
      {children}
    </g>
  )
}
