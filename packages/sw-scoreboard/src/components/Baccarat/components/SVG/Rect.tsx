import { CoordinateType, IRounding } from './types'
import { IStroke } from '../../types'
import { parseCoord } from './helpers'

export interface IRect {
  start?: CoordinateType
  fill?: string
  size: CoordinateType
  stroke?: IStroke
  rounding?: IRounding
  opacity?: number
  className?: string
  x?: number
  y?: number
}

export const Rect = ({
  start,
  fill,
  size,
  stroke = { strokeWidth: 1 },
  rounding = { xRounding: 0, yRounding: 0 },
  opacity,
  ...restProps
}: IRect) => {
  const [left, top]: [number, number] = start ? parseCoord(start) : [0, 0]
  const [width, height]: [number, number] = parseCoord(size)
  const { color, strokeWidth, strokeOpacity } = stroke
  const { xRounding, yRounding } = rounding

  return (
    <rect
      rx={xRounding}
      ry={yRounding}
      fill={fill}
      stroke={color}
      strokeWidth={strokeWidth}
      width={width}
      height={height}
      transform={`translate(${left} ${top})`}
      fillOpacity={opacity}
      strokeOpacity={strokeOpacity}
      {...restProps}
    />
  )
}
