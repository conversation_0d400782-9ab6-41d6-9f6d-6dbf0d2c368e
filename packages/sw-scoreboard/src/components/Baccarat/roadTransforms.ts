import { init, last } from 'ramda'
import { IHistoryItem, MatrixType, RoadDataType } from './types'

const getItemIndex = (columnIndex: number, rowCount: number, offset = 0): number => columnIndex * rowCount + offset

const getIndexToTheRight = (currentItem: number, offset: number, rowCount: number): number =>
  currentItem + offset + rowCount

const clearItem = (roadData: RoadDataType, index: number): RoadDataType => {
  const updateRoadData = [...roadData]
  updateRoadData[index] = undefined
  return updateRoadData
}

const checkRightElement = (
  roadData: RoadDataType,
  clumnStartIndex: number,
  rowCount: number,
  offset: number
): boolean => Boolean(roadData[clumnStartIndex + rowCount + offset])

export const getTailStartIndex = (columnIndex: number, rowCount: number, roadData: RoadDataType): number => {
  const startCurrentColumn = getItemIndex(columnIndex, rowCount)
  const currentColumn = roadData.slice(startCurrentColumn, startCurrentColumn + rowCount)

  for (const index in currentColumn) {
    if (currentColumn[index]) {
      const rowNumber = Number(index)
      const previousRowIndex = rowNumber - 1
      return getIndexToTheRight(startCurrentColumn, previousRowIndex, rowCount)
    }
  }

  const lastRowIndex = rowCount - 1

  return getIndexToTheRight(startCurrentColumn, lastRowIndex, rowCount)
}

export const putVerticalPart = (
  roadData: RoadDataType,
  columnData: IHistoryItem[],
  columnIndex: number,
  rowCount: number
): RoadDataType =>
  columnData.reduce(
    (accumulator: RoadDataType, item: IHistoryItem, idx: number) => {
      const currentItemIndex: number = getItemIndex(columnIndex, rowCount, idx)
      if (currentItemIndex >= accumulator.length) {
        const additional = Array.from({ length: currentItemIndex - accumulator.length }, () => undefined)
        return [...accumulator, ...additional, { ...item }]
      }
      accumulator[currentItemIndex] = { ...item }

      return accumulator
    },
    [...roadData]
  )

export const putHorizontalPart = (
  roadData: RoadDataType,
  tailData: IHistoryItem[],
  columnToStartTail: number,
  rowCount: number,
  tailRow: number
): RoadDataType =>
  tailData.reduce(
    (accumulator: RoadDataType, item: IHistoryItem, idx: number) => {
      const offset = tailRow + idx * rowCount
      const currentItemIndex = getItemIndex(columnToStartTail, rowCount, offset)
      if (currentItemIndex >= accumulator.length) {
        const additional = Array.from({ length: currentItemIndex - accumulator.length }, () => undefined)
        return [...accumulator, ...additional, { ...item }]
      }
      accumulator[currentItemIndex] = { ...item }

      return accumulator
    },
    [...roadData]
  )

const addColumnInRoad = (
  rowCount: number,
  accumulator: RoadDataType,
  column: IHistoryItem[],
  columnIndex: number
): RoadDataType => {
  const tailStartIndex = getTailStartIndex(columnIndex, rowCount, accumulator)

  const tailRow = tailStartIndex % rowCount
  const columnToStartTail = columnIndex + 1
  const verticalPartEnd = tailRow + 1
  const verticalPart = column.slice(0, verticalPartEnd)
  const horizontalPart = column.slice(verticalPartEnd)

  const roadWithNewColumn = putVerticalPart(accumulator, verticalPart, columnIndex, rowCount)
  return putHorizontalPart(roadWithNewColumn, horizontalPart, columnToStartTail, rowCount, tailRow)
}

interface IColumnState {
  accumulator: RoadDataType
  tailDetected: boolean
  tailLength: number
  tailIndex: number
}

export const removeLastColumn = (
  currentRoadData: RoadDataType,
  columnStart: number,
  lastColumn: IHistoryItem[],
  rowCount: number,
  isTie: boolean
): IColumnState => {
  const columnStartIndex = getItemIndex(columnStart, rowCount)
  const cleanedLastColumn = init(lastColumn)
  const columnToUse = isTie ? lastColumn : cleanedLastColumn

  return columnToUse.reduce(
    (
      { accumulator, tailDetected, tailLength, tailIndex }: IColumnState,
      item: IHistoryItem,
      index: number
    ): IColumnState => {
      if (tailDetected) {
        return {
          accumulator: clearItem(accumulator, columnStartIndex + tailIndex + rowCount * tailLength),
          tailDetected,
          tailLength: tailLength + 1,
          tailIndex
        }
      }

      if (checkRightElement(accumulator, columnStartIndex, rowCount, index)) {
        return {
          accumulator: clearItem(accumulator, columnStartIndex + index),
          tailDetected: true,
          tailLength: 1,
          tailIndex: index
        }
      }

      return { accumulator: clearItem(accumulator, columnStartIndex + index), tailDetected, tailIndex, tailLength }
    },
    { accumulator: [...currentRoadData], tailDetected: false, tailLength: 0, tailIndex: 5 }
  )
}

export const roadTransformer = (matrix: MatrixType, rowCount = 6, currentRoadData: RoadDataType = []): RoadDataType => {
  if (currentRoadData.length) {
    const lastColumnStart = matrix.length - 1
    const lastColumn = matrix[lastColumnStart]
    const lastItem = last(lastColumn)
    const isTie = lastItem ? lastItem.isTie : false

    const { accumulator }: IColumnState = removeLastColumn(
      currentRoadData,
      lastColumnStart,
      lastColumn,
      rowCount,
      Boolean(isTie)
    )

    return addColumnInRoad(rowCount, accumulator, lastColumn, lastColumnStart)
  }

  return matrix.reduce(
    (accumulator: RoadDataType, column: IHistoryItem[], columnIndex: number) =>
      addColumnInRoad(rowCount, accumulator, column, columnIndex),
    []
  )
}
