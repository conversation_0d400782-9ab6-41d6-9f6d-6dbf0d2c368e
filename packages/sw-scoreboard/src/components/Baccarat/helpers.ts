import {
  BaccaratPayloadType,
  ColumnType, IBaccaratPayloadItem,
  IHistoryItem,
  IKeyValue,
  IMatrixes,
  IRoads, IShoesData,
  MainBetType, MatrixType, RoadDataType
} from './types'
import { head, init, isNil, last, omit, pathOr } from 'ramda'
import { roadTransformer } from './roadTransforms'
import { strategicTransformer } from './strategicTransforms'


export const transformAllRoads = (payload: ColumnType, matrixesData?: IMatrixes, roads?: IRoads) => {
  const rowCount = 6
  const firstPayloadItem = head(payload)
  const isNewTieItem = firstPayloadItem && firstPayloadItem.isTie && payload.length === 1

  const roadsData = roads
    ? roads
    : {
      bigRoadData: [],
      beadRoadData: [],
      smallRoadData: [],
      bigEyeRoadData: [],
      cockroachRoadData: []
    }

  const matrixes = matrixesData
    ? matrixesData
    : {
      bigRoadMatrix: [],
      beadRoadMatrix: [],
      smallRoadMatrix: [],
      bigEyeRoadMatrix: [],
      cockroachRoadMatrix: []
    }
  const bigRoadMatrix = historyToMatrixTransformer(payload, matrixes.bigRoadMatrix)
  let bigRoadData: RoadDataType = roadTransformer(bigRoadMatrix, rowCount, removePredictions(roadsData.bigRoadData))

  if (roads && isNewTieItem) {
    const tail = last(bigRoadData)
    const tiesNumber = pathOr(0, ['tiesNumber'], last(roadsData.bigRoadData))
    bigRoadData = [...init(bigRoadData), { ...tail, tiesNumber: tiesNumber + 1 }]
  }

  const beadRoadData = [...removeLastPrediction<IHistoryItem | undefined>(roadsData.beadRoadData), ...payload]

  const bigEyeRoadMatrix = isNewTieItem
    ? removePredictionFromLastColumn(matrixes.bigEyeRoadMatrix)
    : strategicTransformer(bigRoadMatrix, 2, matrixes.bigEyeRoadMatrix)
  const bigEyeRoadData = isNewTieItem
    ? removePredictions(roadsData.bigEyeRoadData)
    : roadTransformer(bigEyeRoadMatrix, rowCount, removePredictions(roadsData.bigEyeRoadData))

  const smallRoadMatrix = isNewTieItem
    ? removePredictionFromLastColumn(matrixes.smallRoadMatrix)
    : strategicTransformer(bigRoadMatrix, 3, matrixes.smallRoadMatrix)
  const smallRoadData = isNewTieItem
    ? removePredictions(roadsData.smallRoadData)
    : roadTransformer(smallRoadMatrix, rowCount, removePredictions(roadsData.smallRoadData))

  const cockroachRoadMatrix = isNewTieItem
    ? removePredictionFromLastColumn(matrixes.cockroachRoadMatrix)
    : strategicTransformer(bigRoadMatrix, 4, matrixes.cockroachRoadMatrix)
  const cockroachRoadData = isNewTieItem
    ? removePredictions(roadsData.cockroachRoadData)
    : roadTransformer(cockroachRoadMatrix, rowCount, removePredictions(roadsData.cockroachRoadData))

  return {
    matrixes: { bigRoadMatrix, bigEyeRoadMatrix, smallRoadMatrix, cockroachRoadMatrix },
    roadsData: { bigRoadData, beadRoadData, bigEyeRoadData, smallRoadData, cockroachRoadData }
  }
}

export function removeLastPrediction<T extends IHistoryItem | undefined>(roadData: T[]): T[] {
  const lastItem = last<T>(roadData)

  return lastItem && lastItem.isPrediction ? [...init<any>(roadData)] : roadData
}

export const removePredictionFromLastColumn = (currentMatrix: MatrixType): MatrixType => {
  const lastColumn: ColumnType | undefined = last(currentMatrix)

  return lastColumn ? [...init(currentMatrix), removeLastPrediction<IHistoryItem>(lastColumn)] : currentMatrix
}

export const allShoeHistoryPayloadFormatter = (arrayOfShoes: BaccaratPayloadType[]): IShoesData => {
  const transformedData = arrayOfShoes.map((shoe: BaccaratPayloadType) => historyTransformer(shoe))

  return {
    totalShoeHistory: transformedData.reduce((prev, current) => [...prev, ...current], []),
    currentShoeHistory: transformedData[2],
    lastShoeHistory: transformedData[1],
    preLastShoeHistory: transformedData[0]
  }
}

export const historyTransformer = (history: BaccaratPayloadType): IHistoryItem[] => {
  const tieIndicesSet = getTieMap(history)

  return (Array.isArray(history) ? history : []).map(
    (
      { points: { playerPoints, bankerPoints }, outcome, bankerPair, playerPair }: IBaccaratPayloadItem,
      index: number
    ) => ({
      isBankerPair: bankerPair,
      isPlayerPair: playerPair,
      isPrediction: false,
      isTie: bankerPoints === playerPoints,
      outcome,
      point: Math.max(bankerPoints, playerPoints),
      tiesNumber: tieIndicesSet[index]
    })
  )
}

const getTieMap = (history: BaccaratPayloadType): IKeyValue<number> =>
  (Array.isArray(history) ? history : []).reduce((acc: {[key: number]: number}, item, index) => {
    const {
      points: { playerPoints, bankerPoints }
    } = item
    if (playerPoints === bankerPoints) {
      if (index !== 0 && !isNil(acc[index - 1])) {
        return { ...omit([index - 1], acc), [index]: acc[index - 1] + 1 }
      }
      return { ...acc, [index]: 1 }
    }
    return acc
  }, {})


export const historyToMatrixTransformer = (history: ColumnType, currentMatrix: MatrixType = []) =>
  history.reduce((accumulator, current) => {
    const { isTie, outcome, tiesNumber } = current
    const next = { ...current }
    const isFirstMove: boolean = accumulator.length === 0
    const lastColumn: ColumnType = last(accumulator) || []
    const lastItem: IHistoryItem | undefined = last(lastColumn)

    if (isFirstMove || !lastItem) {
      return [[next]]
    }

    const { outcome: previousOutcome, isPrediction: isPreviousPrediction } = lastItem

    const handIsChanged: boolean = !isTie && outcome !== previousOutcome

    if (isPreviousPrediction) {
      if (isTie) {
        return addTieAfterPrediction(accumulator, lastColumn)
      }
      return addNewItemAfterPrediction(next, handIsChanged, accumulator, lastColumn)
    }

    if (handIsChanged) {
      return [...accumulator, [next]]
    }

    if (isTie && lastItem) {
      return [...init(accumulator), [...init(lastColumn), { ...lastItem, isTie: true, tiesNumber }]]
    }

    return [...init(accumulator), [...lastColumn, next]]
  }, currentMatrix)

export const addTieAfterPrediction = (road: MatrixType, lastColumn: ColumnType): IHistoryItem[][] => {
  const lastColumnWithoutPrediction = init(lastColumn)

  if (lastColumnWithoutPrediction && lastColumnWithoutPrediction.length) {
    const lastItem: IHistoryItem | undefined = last(lastColumnWithoutPrediction)

    if (lastItem) {
      return [...init(road), [...init(lastColumnWithoutPrediction), { ...lastItem, isTie: true }]]
    }
  }

  const roadWithoutEmptyColumn = init(road)
  if (!roadWithoutEmptyColumn.length) {
    return [[{ isTie: true, isPrediction: false, outcome: MainBetType.Tie }]]
  }
  const newLastColumn = last(roadWithoutEmptyColumn) || []
  const newLastItem: IHistoryItem | undefined = last(newLastColumn)

  return newLastItem
    ? [...init(roadWithoutEmptyColumn), [...init(newLastColumn), { ...newLastItem, isTie: true }]]
    : road
}

export const addNewItemAfterPrediction = (
  newItem: IHistoryItem,
  isHandChanged: boolean,
  road: MatrixType,
  lastColumn: ColumnType
) => {
  const lastColumnWithoutPrediction = init(lastColumn)

  if (isHandChanged) {
    if (lastColumnWithoutPrediction && lastColumnWithoutPrediction.length) {
      return [...init(road), [...lastColumnWithoutPrediction], [newItem]]
    }

    const accumulatorWithoutLastColumn = init(road)
    const newLast = last(accumulatorWithoutLastColumn) || []

    return [...init(accumulatorWithoutLastColumn), [...newLast, newItem]]
  }

  return [...init(road), [...init(lastColumn), newItem]]
}

export const removePredictions = (roadData: RoadDataType = []) =>
  roadData.reduce(
    (accumulator: RoadDataType, next: IHistoryItem | undefined, index: number) => {
      const result = [...accumulator]

      if (next && next.isPrediction) {
        result[index] = undefined
      }

      return result
    },
    [...roadData]
  )
