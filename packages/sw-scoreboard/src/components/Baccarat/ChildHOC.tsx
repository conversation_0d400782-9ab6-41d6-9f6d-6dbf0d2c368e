import React, { Component, FunctionComponent } from 'react'

import { ICircleItem, ILineItem } from './types'

export const ChildHOC = function ChildHOC(
  ChildComponent: FunctionComponent<ICircleItem> | FunctionComponent<ILineItem>
) {
  return class WrappedChild extends Component<ICircleItem> {
    public shouldComponentUpdate(nextProps: ICircleItem) {
      const { size, children, radius, symbol } = this.props

      const needRerender = size !== nextProps.size || radius !== nextProps.radius

      if (needRerender) {
        return true
      }

      const shouldRerender =
        children.isPrediction !== nextProps.children.isPrediction ||
        children.isTie !== nextProps.children.isTie ||
        children.outcome !== nextProps.children.outcome ||
        children.tiesNumber !== nextProps.children.tiesNumber ||
        symbol !== nextProps.symbol ||
        (!!children.point && children.point !== nextProps.children.point)

      return children ? shouldRerender : false
    }

    public render() {
      return <ChildComponent {...this.props} />
    }
  }
}
