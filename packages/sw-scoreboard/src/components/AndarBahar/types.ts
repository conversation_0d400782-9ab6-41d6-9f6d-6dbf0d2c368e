export type AndarBaharPayloadType = IAndarBaharPayloadItem[];

export interface IAndarBaharPayloadItem {
  outcome: MainBet;
  numberOfCards: number;
  winCard: ICard;
}

export enum MainBet {
  Andar = "andar",
  Bahar = "bahar",
}

interface ICard {
  rank: CardRank;
  suit: CardSuit;
  deck: number;
}

enum CardHighRank {
  Jack = "jack",
  Queen = "queen",
  <PERSON> = "king",
  Ace = "ace",
  Joker = "joker",
}

declare enum CardSuit {
  Clubs = "clubs",
  Diamonds = "diamonds",
  Hearts = "hearts",
  Spades = "spades",
}

export enum CardRank {
  UNKNOWN = 0,
  TWO = 2,
  THREE = 3,
  FOUR = 4,
  FIVE = 5,
  SIX = 6,
  SEVEN = 7,
  EIGHT = 8,
  NINE = 9,
  TEN = 10,
  JACK = 11,
  QUEEN = 12,
  KING = 13,
  ACE = 14,
}
export const cardsValueShorthandMap: any = {
  [CardHighRank.Ace]: "A",
  [CardHighRank.King]: "K",
  [CardHighRank.Queen]: "Q",
  [CardHighRank.Jack]: "J",
};

