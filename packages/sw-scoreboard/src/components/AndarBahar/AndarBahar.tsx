import { AndarBaharItem } from '@/components/AndarBahar/component/Item/AndarBaharItem'
import { AndarBaharPayloadType, IAndarBaharPayloadItem } from '@/components/AndarBahar/types'
import { ANDAR_BAHAR_SCOREBOARD_CLASSNAME as CLASSNAME, ITEMS_COUNT } from './const'
import './styles.scss'

interface IProps {
  payload: AndarBaharPayloadType;
  className?: string
}

export function AndarBahar({ payload, className }: IProps) {
  return (
    <div className={`${CLASSNAME} ${className ?? ''}`}>
      <div className={`${CLASSNAME}__wrapper`}>
        {payload.slice(0, ITEMS_COUNT + 1).map((item: IAndarBaharPayloadItem, idx) => (
          <AndarBaharItem
            outcome={item?.outcome}
            numberOfCards={item?.numberOfCards}
            rank={item?.winCard.rank}
            key={`${idx}-${item?.outcome}-${item?.winCard.rank}`}
          />
        ))}
      </div>
    </div>
  )
}
