.sw-andarbahar-scoreboard-item {
  $this: &;
  font-family: <PERSON>to, Arial, sans-serif;
  line-height: 2em;
  border-radius: 0.3em;
  height: 92%;
  width: 8%;
  animation: 1s ease-out forwards win-history-item;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;

  background-color: rgba(34, 34, 34, 0.7);

  &_andar {
    #{$this}__value {
      background-color: rgba(#1b76ee, 0.8);
    }

    #{$this}__cards-count {
      color: #1b76ee;
    }
  }

  &_bahar {
    #{$this}__value {
      background-color: rgba(#ec2033, 0.8);
    }

    #{$this}__cards-count {
      color: #ec2033;
    }
  }

  &_empty {
    background-color: transparent;
  }

  &:first-child {
    #{$this}__value {
      border: 0.0625em solid #ffb700;
    }
  }

  &__value {
    height: 50%;
    box-sizing: border-box;
    border-radius: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;

    &:first-child {
      border: inherit;
    }
  }

  &__value,
  &__cards-count {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;

    > div {
      font-size: 1em;
      font-weight: 700;
    }
  }
}

@keyframes win-history-item {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translate(0, 0);
  }
}
