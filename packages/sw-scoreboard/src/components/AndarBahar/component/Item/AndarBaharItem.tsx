import { getCardRankSymbol } from '@/components/AndarBahar/helpers'
import { CardRank, MainBet } from '@/components/AndarBahar/types'
import cn from 'classnames'
import { ANDAR_BAHAR_SCOREBOARD_ITEM_CLASSNAME as CLASSNAME } from './const'
import './styles.scss'

interface IProps {
  outcome: MainBet;
  numberOfCards: number;
  rank: CardRank;
}

export function AndarBaharItem({
  outcome,
  rank,
  numberOfCards
}: IProps) {
  return (
    <div
      className={cn(CLASSNAME, {
        [`${CLASSNAME}_${outcome}`]: outcome,
        [`${CLASSNAME}_empty`]: !rank,
      })}
    >
      <div className={cn(`${CLASSNAME}__value`)}>
        <div>{getCardRankSymbol(rank)}</div>
      </div>
      <div className={`${CLASSNAME}__cards-count`}>
        <div>{numberOfCards}</div>
      </div>
    </div>
  );
}
