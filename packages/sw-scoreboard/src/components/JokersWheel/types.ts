export type JokersWheelPayloadType = IJokersWheelPayloadItem[]

interface IJokersWheelPayloadItem {
  roundId?: string
  wheelResult: SpotType
  status?: string
}

export type SpotType = SpotSimpleType | SpotBonusType

export enum SpotSimpleType {
  Bet1 = '1',
  Bet2 = '2',
  Bet5 = '5',
  Bet10 = '10'
}

export enum SpotBonusType {
  Luck = 'luck',
  Dice = 'dice',
  Deluxe = 'deluxe',
  Wheel = 'wheel'
}
