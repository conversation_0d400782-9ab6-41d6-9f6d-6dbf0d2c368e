import { IconBet } from '@/components/JokersWheel/components/IconBet/IconBet'
import { SpotType } from '@/components/JokersWheel/types'
import './JokersWheelItem.scss'


interface Props {
  index: number
  wheelResult: SpotType
}

export const JokersWheelItem = ({ index, wheelResult}: Props) => {
  return (
    <div className="sw-jw-scoreboard-item">
      <IconBet className="sw-jw-scoreboard-item__icon" type={wheelResult} isHighlighted={index === 0} />
    </div>
  )
}
