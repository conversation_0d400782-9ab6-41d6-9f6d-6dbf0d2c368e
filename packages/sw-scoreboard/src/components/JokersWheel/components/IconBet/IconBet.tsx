import cn from 'classnames'
import { SpotType } from '../../../JokersWheel/types'
import './styles.scss'

interface IProps {
  type: SpotType
  isHighlighted?: boolean
  className?: string
}

export function IconBet({ type, isHighlighted, className }: IProps) {
  return (
    <div className={cn('sw-jw-scoreboard-icon-bet', `sw-jw-scoreboard-icon-bet_${type}`, {
      [`sw-jw-scoreboard-icon-bet_${type}_highlighted`]: isHighlighted
    }, className)} />
  )
}
