.sw-jw-scoreboard-icon-bet {
  background-repeat: no-repeat;
  background-size: 95%;
  background-position: center;
  width: 100%;
  height: 100%;

  &_luck {
    &:not(&_highlighted) {
      background-image: url("./images/luck_default.svg");
    }
    &_highlighted {
      background-image: url(./images/luck_active.svg);
    }
  }

  &_dice {
    &:not(&_highlighted) {
      background-image: url("./images/dice_default.svg");
    }

    &_highlighted {
      background-image: url(./images/dice_active.svg);
    }
  }

  &_deluxe {
    &:not(&_highlighted) {
      background-image: url("./images/deluxe_default.svg");
    }

    &_highlighted {
      background-image: url(./images/deluxe_active.svg);
    }
  }

  &_wheel {
    &:not(&_highlighted) {
      background-image: url("./images/wheel_default.svg");
    }

    &_highlighted {
      background-image: url(./images/wheel_active.svg);
    }
  }

  &_1 {
    &:not(&_highlighted) {
      background-image: url("./images/1_default.svg");
    }
    &_highlighted {
      background-image: url("./images/1_active.svg");
    }
  }

  &_2 {
    &:not(&_highlighted) {
      background-image: url("./images/2_default.svg");
    }
    &_highlighted {
      background-image: url("./images/2_active.svg");
    }
  }

  &_5 {
    &:not(&_highlighted) {
      background-image: url("./images/5_default.svg");
    }
    &_highlighted {
      background-image: url("./images/5_active.svg");
    }
  }

  &_10 {
    &:not(&_highlighted) {
      background-image: url("./images/10_default.svg");
    }
    &_highlighted {
      background-image: url("./images/10_active.svg");
    }
  }
}
