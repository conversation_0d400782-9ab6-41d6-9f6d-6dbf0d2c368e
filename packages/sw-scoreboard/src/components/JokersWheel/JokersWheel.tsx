import { JokersWheelPayloadType } from '@/components/JokersWheel/types'
import { ScoreboardSlider } from '@/components/ScoreboardSlider/Slider'
import { JokersWheelItem } from '@/components/JokersWheel/components/JokersWheelItem/JokersWheelItem'


interface IProps {
  payload: JokersWheelPayloadType
  className?: string
}

export function JokersWheel({ payload, className }: IProps) {
  const items = Array.isArray(payload) ? payload : []
  const key =`sw-jw-scoreboard-${payload[0]?.roundId}`
  
  return (
    <ScoreboardSlider updateKey={key} className={className}>
      {items.map(( item, index ) => (
        <JokersWheelItem key={item.roundId || index} index={index} wheelResult={item.wheelResult} />
      ))}
    </ScoreboardSlider>
  )
}
