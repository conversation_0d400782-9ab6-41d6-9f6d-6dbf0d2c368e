import { AndarBahar } from '@/components/AndarBahar/AndarBahar'
import type { AndarBaharPayloadType } from '@/components/AndarBahar/types'
import { Baccarat } from './components/Baccarat/Baccarat'
import type { BaccaratPayloadType } from './components/Baccarat/types'
import { JokersWheel } from './components/JokersWheel/JokersWheel'
import type { JokersWheelPayloadType } from './components/JokersWheel/types'
import { Roulette } from './components/Roulette/Roulette'
import type { RoulettePayloadType } from './components/Roulette/types'
import type { ISwScoreboard } from '@/types'
import { GameType } from 'sw-live-core'
import { SicBo } from '@/components/SicBo/SicBo'
import type { SicBoPayloadType } from '@/components/SicBo/types'


export const SwScoreboard = ({ type, payload, ...restProps }: ISwScoreboard) => {
  switch (type) {
    case GameType.ROULETTE: {
      return <Roulette payload={payload as RoulettePayloadType} {...restProps} />
    }
    case GameType.BACCARAT:
    case GameType.DRAGON_TIGER: {
      return <Baccarat type={type} payload={payload as BaccaratPayloadType} {...restProps} />
    }
    case GameType.JOKERS_WHEEL: {
      return <JokersWheel payload={payload as JokersWheelPayloadType} {...restProps} />
    }
    case GameType.ANDAR_BAHAR: {
      return <AndarBahar payload={payload as AndarBaharPayloadType} {...restProps} />
    }
    case GameType.SIC_BO: {
      return <SicBo payload={payload as SicBoPayloadType} {...restProps} />
    }
    default:
      return null
  }
}
