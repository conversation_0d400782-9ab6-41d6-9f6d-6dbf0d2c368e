{"name": "sw-scoreboard", "scripts": {"clean": "rm -rf node_modules package-lock.json dist", "build": "vite build", "dev": "vite build --watch"}, "devDependencies": {"@types/ramda": "^0.30.2", "@types/react": "19.0.4", "@types/react-dom": "19.0.2", "@vitejs/plugin-react": "4.3.4", "classnames": "^2.5.1", "ramda": "^0.30.1", "rollup-plugin-bundle-scss": "^0.1.3", "sass": "^1.83.1", "typescript": "^5.7.3", "vite": "^6.0.7", "vite-plugin-dts": "^4.5.0"}, "peerDependencies": {"sw-live-core": "*", "ramda": "^0.30.1", "react": "^19.0.0", "react-dom": "^19.0.0"}, "main": "./dist/sw-scoreboard.umd.cjs", "module": "./dist/sw-scoreboard.es.js", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/sw-scoreboard.es.js"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/sw-scoreboard.umd.cjs"}}, "./dist/style.css": "./dist/style.css", "./dist/index.scss": "./dist/index.scss"}, "files": ["dist"]}