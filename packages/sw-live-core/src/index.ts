export enum GameType {
  BACCARAT = 0,
  ROULETTE = 1,
  B<PERSON><PERSON>KJACK = 2,
  JP_SHOW = 3,
  DRAGON_TIGER = 4,
  JOKERS_WHEEL = 5,
  BLACKJACK_MAX = 6,
  <PERSON><PERSON><PERSON>KJACK_SEVEN_SEAT = 7,
  ANDAR_BAHAR = 8,
  ROCKET = 9,
  TEEN_PATTI = 10,
  SIC_BO = 11
}

export interface DealerInfo {
  name: string
  picture: string
  isVirtual?: boolean
}

export enum TableStatus {
  ONLINE = 'online',
  OFFLINE = 'offline'
}

export namespace Blackjack {
  export type Seat = 'D' | 'P1' | 'P2' | 'P3' | 'P4' | 'P5' | 'P6' | 'P7' | string
  export type PlayerSeat = Exclude<Seat, 'D'>

  export interface SeatState {
    seat: Seat
    playerCode?: string
  }
}
