{"name": "sw-live-core", "scripts": {"clean": "rm -rf node_modules package-lock.json dist", "build": "vite build", "dev": "vite build --watch"}, "devDependencies": {"typescript": "^5.7.3", "vite": "^6.1.0", "vite-plugin-dts": "^4.5.0"}, "main": "./dist/sw-live-core.umd.cjs", "module": "./dist/sw-live-core.es.js", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/sw-live-core.es.js"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/sw-live-core.umd.cjs"}}}, "files": ["dist"]}