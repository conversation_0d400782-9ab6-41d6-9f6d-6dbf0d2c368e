import { resolve } from 'node:path';
import { defineConfig } from 'vite';
import dts from 'vite-plugin-dts';
import bundleScss from 'rollup-plugin-bundle-scss';

export default defineConfig({
  resolve:{
    alias:{
      '@' : resolve(__dirname, './src'),
    },
  },
  plugins: [
    dts({
      insertTypesEntry: true,
    }),
    bundleScss({
      exclusive: false
    }),
  ],
  build: {
    sourcemap: true,
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'sw-live-core',
      formats: ['es', 'umd'],
      fileName: (format) => `sw-live-core.${format}.js`,
    }
  }
});
