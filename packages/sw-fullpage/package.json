{"name": "sw-fullpage", "scripts": {"clean": "rm -rf node_modules package-lock.json dist", "build": "vite build", "dev": "vite build --watch"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/lodash.get": "^4.4.9", "@types/ramda": "^0.30.2", "@types/react": "19.0.4", "@types/react-dom": "19.0.2", "@vitejs/plugin-react": "4.3.4", "classnames": "^2.5.1", "ramda": "^0.30.1", "rollup-plugin-bundle-scss": "^0.1.3", "sass": "^1.83.1", "typescript": "^5.7.3", "vite": "^6.0.7", "vite-plugin-dts": "^4.5.0"}, "peerDependencies": {"ramda": "^0.30.1", "react": "^19.0.0", "react-dom": "^19.0.0", "sw-live-core": "*"}, "main": "./dist/sw-fullpage.umd.cjs", "module": "./dist/sw-fullpage.es.js", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/sw-fullpage.es.js"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/sw-fullpage.umd.cjs"}}, "./dist/style.css": "./dist/style.css", "./dist/index.scss": "./dist/index.scss"}, "files": ["dist"], "dependencies": {"lodash-es": "^4.17.21"}}