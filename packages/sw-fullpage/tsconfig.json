{"compilerOptions": {"baseUrl": "./", "target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "isolatedModules": true, "strict": true, "jsx": "react-jsx", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable"], "skipLibCheck": true, "outDir": "dist", "declaration": true}, "include": ["src/**/*"]}