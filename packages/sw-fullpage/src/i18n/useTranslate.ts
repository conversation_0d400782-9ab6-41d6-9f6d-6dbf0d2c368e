import { useContext } from 'react'
import { locales } from '@/i18n/locales'
import { get } from 'lodash-es'
import type { LocaleValue } from '@/i18n/model'
import { TranslateContext } from '@/i18n/TranslateContext'

interface UseTranslateResult {
  t: (key: string) => string;
}

export const useTranslate = (): UseTranslateResult => {
  const context = useContext(TranslateContext)
  if (!context) {
    throw new Error('Missing TranslateContext.Provider in the tree')
  }
  const { translations, defaultLocale } = context

  const t = (path: string, params?: Record<string, string>) => {
    const data = translations ?? locales[defaultLocale]
    let record = get(data as unknown, path) as LocaleValue
    if (typeof record === 'string') {
      if (params) {
        for (const [name, value] of Object.entries(params)) {
          record = record.replace(new RegExp(`{${name}}`, 'g'), value)
        }
      }
      return record
    }
    return path
  }

  return { t }
}
