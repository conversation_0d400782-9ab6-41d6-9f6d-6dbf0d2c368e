import type { PropsWithChildren } from 'react'
import { useMemo } from 'react'
import { Language } from '@/i18n/model'
import { locales } from '@/i18n/locales'
import { TranslateContext } from '@/i18n/TranslateContext'

const defaultLanguage = Language.EN

interface Props {
  language?: string
}

export const TranslateProvider = ({ language, children }: PropsWithChildren<Props>) => {

  const state = useMemo(() => {
    const locale = language ? (language as Language) : defaultLanguage
    return ({
      locale,
      translations: locales[locale],
      defaultLocale: defaultLanguage
    })
  }, [language])

  return (
    <TranslateContext.Provider value={{ ...state }}>
      {children}
    </TranslateContext.Provider>
  )
}
