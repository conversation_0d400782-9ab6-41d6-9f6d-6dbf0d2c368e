export enum Language {
  EN = 'en',
  ZHCN = 'zh-cn',
  ZHTW = 'zh-tw',
  JA = 'ja',
  MS = 'ms',
  KO = 'ko',
  TH = 'th',
  VI = 'vi',
  ID = 'id',
  RO = 'ro',
  IT = 'it',
  EL = 'el',
  KM = 'km',
  PT = 'pt',
  ES = 'es',
  RU = 'ru',
  DE = 'de',
  SV = 'sv',
  DA = 'da',
  NL = 'nl',
  PTBR = 'pt-br',
  BG = 'bg',
  SR = 'sr',
  TR = 'tr'
}

export interface LocalesType {
  [key: string]: LocaleValue
}

export type LocaleValue = string | LocalesType
