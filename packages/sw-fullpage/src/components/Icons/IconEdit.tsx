interface Props {
  className?: string
}

export const IconEdit = ({ className }: Props) => {
  return (
    <svg className={`icon-edit ${className ?? ''}`} version="1.1" xmlns="http://www.w3.org/2000/svg"
         viewBox="0 0 32 32">
      <path
        d="M0 25.339v6.667h6.667l19.659-19.659-6.667-6.667zM31.48 7.184c0.321-0.321 0.52-0.764 0.52-1.253s-0.199-0.933-0.52-1.253l-4.16-4.16c-0.321-0.321-0.764-0.52-1.253-0.52s-0.933 0.199-1.253 0.52l-3.253 3.253 6.667 6.667z"></path>
    </svg>
  )
}
