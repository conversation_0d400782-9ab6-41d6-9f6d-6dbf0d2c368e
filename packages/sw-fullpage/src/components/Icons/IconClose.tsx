interface Props {
  className?: string
}

export const IconClose = ({ className }: Props) => {
  return (
    <svg className={`icon-close ${className ?? ''}`} version="1.1" xmlns="http://www.w3.org/2000/svg"
         viewBox="0 0 32 32">
      <path
        d="M19.077 15.997l12.289 12.287c0.393 0.393 0.636 0.937 0.636 1.537 0 1.201-0.973 2.174-2.174 2.174-0.601 0-1.144-0.244-1.538-0.637v0l-12.289-12.283-12.289 12.287c-0.393 0.394-0.937 0.637-1.538 0.637-1.201 0-2.174-0.973-2.174-2.174 0-0.6 0.243-1.143 0.636-1.537l12.289-12.291-12.277-12.285c-0.393-0.393-0.636-0.937-0.636-1.537 0-1.201 0.973-2.174 2.174-2.174 0.601 0 1.144 0.244 1.538 0.637l12.289 12.287 12.277-12.275c0.393-0.394 0.937-0.637 1.538-0.637 1.201 0 2.174 0.973 2.174 2.174 0 0.6-0.243 1.143-0.636 1.537v0z"></path>
    </svg>
  )
}
