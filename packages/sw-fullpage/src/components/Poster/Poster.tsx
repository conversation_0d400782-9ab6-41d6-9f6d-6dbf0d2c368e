import { useMemo } from 'react'
import { BASE_URL, FullPageGameType } from '@/model'
import './Poster.scss'

interface Props {
  gameType: FullPageGameType
  dealerPicture?: string
  isOnline?: boolean
}

export const Poster = ({ gameType, dealerPicture, isOnline }: Props) => {
  const src = useMemo(() => {
    if (isOnline && dealerPicture) {
      return dealerPicture
    }
    if ([FullPageGameType.JokersWheel, FullPageGameType.BlackjackMax].includes(gameType)) {
      return `${BASE_URL}/${gameType}/images/default-thumb.png`
    }
    return null
  }, [dealerPicture, gameType, isOnline])

  if (!src) {
    return null
  }
  return (
    <div className="poster">
      <img className="poster__img" src={src} alt="" />
    </div>
  )
}
