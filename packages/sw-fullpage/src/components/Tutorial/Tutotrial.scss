@use "../../styles/variables";

.tutorial {
  $this: &;

  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;

  &__title {
    display: none;
    padding: 1em 0 0.5em;
    font-size: 2em;
    font-weight: 600;
    color: variables.$color-highlight;
    text-transform: uppercase;
    text-align: center;
  }

  video {
    height: auto;
    max-height: calc(100% - 2em);
    width: 100%;
    outline: none;
  }

  &__button {
    position: absolute;
    top: 4em;
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 2em;
    font-family: Lato, Arial, sans-serif;
    font-size: 1.5em;
    font-weight: 600;
    text-transform: uppercase;
    color: #000;
    outline: none;
    border: none;
    cursor: pointer;

    &_play {
      background: linear-gradient(to right, transparent, #ffba00, #ffba00, transparent);
    }

    &_stop {
      display: none;
      background: linear-gradient(to right, transparent, #8d8d8d, #8d8d8d, transparent);
    }
  }

  &__poster {
    position: absolute;
    top: 2em;
    left: 0;
    width: 100%;
    height: 100%;

    img {
      display: block;
      max-width: 100%;
      max-height: 100%;
      margin: 0 auto;
    }
  }

  &__close {
    position: absolute;
    top: 2em;
    right: 2em;
    width: 2em;
    height: 2em;
    svg {
      fill: #8d8d8d;
      display: block;
      height: 100%;
      width: 100%;
    }
  }

  .desktop & {
    &_playing:hover {
      #{$this}__button_stop {
        display: flex;
      }
    }
  }

  .mobile & {
    &__button {
      &_stop {
        display: none;
      }
    }

    &_playing {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 3;
      background: #000;

      #{$this}__title {
        display: block;
      }
    }

    @media (orientation: portrait) {
      &__button {
        top: 6em;
      }
    }

    @media (orientation: landscape){
      max-width: 60em;

      &_playing {
        #{$this}__title {
          font-size: 4em;
        }
      }

      &__button {
        top: 3em;
        font-size: 2.5em;
      }

      &_playing {
        max-width: 100%;

        video {
          height: 70%;
          width: auto;
          margin: auto;
        }
      }

      &__close {
        top: 4em;
        right: 4em;
        width: 4em;
        height: 4em;
      }
    }
  }
}
