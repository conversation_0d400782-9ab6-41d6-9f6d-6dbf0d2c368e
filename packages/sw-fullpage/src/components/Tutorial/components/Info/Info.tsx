import './Info.scss'
import { useTranslate } from '@/i18n/useTranslate'

interface Props {
  isPlaying: boolean
}

export const Info = ({ isPlaying }: Props) => {
  const { t } = useTranslate()

  return (
    <div className={`info ${isPlaying ? 'info_playing' : ''}`}>
      <div className="info__part">
        <div className="info__item info__item_1">
          {t('selectChip')}
        </div>
        <div className="info__item info__item_2">
          {t('placeYourBets')}
        </div>
        <div className="info__item info__item_3">
          {t('andPressSpin')}
        </div>
      </div>

      <div className="info__part">
        <div className="info__item info__item_4">
          {t('perfectTableIs')}
        </div>
        <div className="info__item info__item_5">
          {t('selectedForYou')}
        </div>
      </div>
    </div>
  )
}
