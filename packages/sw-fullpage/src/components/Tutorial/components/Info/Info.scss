@use '../../../../styles/variables';

$color-complete: #fff;
$animate-color-duration: 0.1s;

.info {
  $this: &;

  position: relative;
  width: 100%;
  height: 2em;
  color: #444444;
  font-weight: 600;

  &_playing {
    #{$this}__part {
      &:first-child {
        animation: 0.1s hide<PERSON>hrase 9s linear forwards;
      }

      &:last-child{
        animation: 0.1s showPhrase 9s linear forwards;
      }
    }

    #{$this}__item {
      &_1 {
        animation: $animate-color-duration toYellow 1.8s linear forwards, $animate-color-duration yellowToWhite 3.3s linear forwards;
      }

      &_2 {
        animation: $animate-color-duration toYellow 3.3s linear forwards, $animate-color-duration yellowToWhite 7.6s linear forwards;
      }

      &_3 {
        animation: $animate-color-duration toYellow 7.6s linear forwards, 0.1s hidePhrase 9s linear forwards;
      }

      &_4 {
        animation: $animate-color-duration toWhite 10.2s linear forwards;
      }

      &_5 {
        animation: $animate-color-duration toYellow 10.2s linear forwards;
      }
    }
  }

  &__part {
    position: absolute;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;

    &:last-child{
      opacity: 0;
    }
  }

  &__item {
    font-size: 1.05em;
    padding: 0 .2em;
  }

  .mobile & {
    &__item {
      text-transform: uppercase;
    }

    @media (orientation: portrait) {
      &__item {
        font-size: 1.2em;
        text-transform: uppercase;
      }

      &_playing {
        height: 15%;
        padding: 5% 0 0;
        text-transform: uppercase;

        #{$this}__item {
          font-size: 1.3em;
        }

        #{$this}__part {
          height: auto;
          flex-wrap: wrap;
          align-items: flex-start;

          &:first-child {
            #{$this}__item {
              padding: 0.2em;

              &:last-child {
                width: 100%;
                flex-shrink: 0;
                text-align: center;
              }
            }
          }
        }
      }
    }

    @media (orientation: landscape) {
      &__item {
        font-size: 2em;
      }

      &_playing {
        #{$this}__item {
          font-size: 3em;
        }
      }
    }
  }
}

@keyframes toYellow {
  to {
    color: variables.$color-highlight;
  }
}

@keyframes toWhite {
  to {
    color: $color-complete;
  }
}

@keyframes yellowToWhite {
  from {
    color: variables.$color-highlight;
  }
  to {
    color: $color-complete;
  }
}

@keyframes hidePhrase {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}


@keyframes showPhrase {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes disappearAnimation {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
