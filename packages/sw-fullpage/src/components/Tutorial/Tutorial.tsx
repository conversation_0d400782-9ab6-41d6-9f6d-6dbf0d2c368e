import type { MouseEvent } from 'react'
import { useEffect, useRef, useState } from 'react'
import './Tutotrial.scss'
import type { FullPageGameType, Orientations } from '@/model'
import { BASE_URL } from '@/model'
import { IconClose } from '@/components/Icons/IconClose'
import { useTranslate } from '@/i18n/useTranslate'
import { Info } from '@/components/Tutorial/components/Info/Info'

interface Props {
  isMobile: boolean
  orientation: Orientations
  gameType: FullPageGameType
  className?: string
}

interface TutorialSource {
  videoSrc: string
  posterSrc: string
}

export const Tutorial = ({ gameType, className, orientation, isMobile }: Props) => {
  const { t } = useTranslate()
  const refVideo = useRef<HTMLVideoElement>(null)
  const [tutorialSource, setTutorialSource] = useState<TutorialSource | undefined>()
  const [isPlaying, setIsPlaying] = useState(false)
  const prevVideoSrcRef = useRef('')

  useEffect(() => {
    const videoSrc = `${BASE_URL}/${gameType}/video/${orientation}.mp4`
    const posterSrc = `${BASE_URL}/${gameType}/images/${orientation}/poster.png`

    if (videoSrc !== prevVideoSrcRef?.current) {
      void stop()
      prevVideoSrcRef.current = videoSrc
    }

    setTutorialSource({
      videoSrc,
      posterSrc
    })

  }, [gameType, orientation])


  const play = async (): Promise<void> => {
    if (refVideo?.current) {
      await refVideo.current.play()
      setIsPlaying(true)
    }
  }

  const stop = async (): Promise<void> => {
    if (refVideo?.current && setIsPlaying) {
      await refVideo.current.play()
      setIsPlaying(false)
      refVideo.current.currentTime = 0
    }
  }

  const handlePlayClick = (e: MouseEvent) => {
    e.stopPropagation()
    void play()
  }

  const handleStopClick = (e: MouseEvent) => {
    e.stopPropagation()
    void stop()
  }

  const handleVideoClick = (e: MouseEvent) => {
    e.stopPropagation()
    if (isPlaying) {
      void stop()
    } else {
      void play()
    }
  }

  const handleVideoEnded = () => {
    void stop()
    void play()
  }

  const handleCloseClick = (e: MouseEvent) => {
    e.stopPropagation()
    void stop()
  }

  return (
    <div className={`tutorial ${isPlaying ? 'tutorial_playing' : ''} ${className ?? ''}`}>
      <div className="tutorial__title">{t('howToPlay')}</div>
      <Info isPlaying={isPlaying} />
      {tutorialSource?.videoSrc &&
        <video
          ref={refVideo}
          src={tutorialSource.videoSrc}
          muted
          preload="metadata"
          playsInline
          onClick={handleVideoClick}
          onEnded={handleVideoEnded}
        />
      }
      {isMobile && isPlaying && <div className="tutorial__close" onClick={handleCloseClick}>
        <IconClose />
      </div>}
      {!isPlaying && tutorialSource?.posterSrc && <div className="tutorial__poster" onClick={handlePlayClick}>
        <img src={tutorialSource.posterSrc} alt="" />
      </div>}
      {!isPlaying && <button className="tutorial__button tutorial__button_play" onClick={handlePlayClick}>
        {t('pressWatch')}
      </button>}
      {isPlaying && <button className="tutorial__button tutorial__button_stop" onClick={handleStopClick}>
        {t('pressStop')}
      </button>}
    </div>
  )
}
