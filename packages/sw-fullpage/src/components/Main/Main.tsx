import { Tutorial } from '@/components/Tutorial/Tutorial'
import { Poster } from '@/components/Poster/Poster'
import type { SwFullPageProps } from '@/model'
import { BASE_URL, Orientations, FullPageGameType } from '@/model'
import { IconEdit } from '@/components/Icons/IconEdit'
import './Main.scss'
import { useTranslate } from '@/i18n/useTranslate'

export const Main = ({
                       gameType,
                       isOnline,
                       onPlay,
                       isMobile,
                       isPortrait,
                       selectedLimitString,
                       isLimitEditable,
                       onLimitEdit,
                       dealerPicture
                     }: SwFullPageProps) => {
  const { t } = useTranslate()

  const orientation = isMobile ? (isPortrait ? Orientations.Portrait : Orientations.Landscape) : Orientations.Desktop
  const backgroundImage = `url(${`${BASE_URL}/${gameType}/images/${orientation}/promo_bg.png`})`
  const isTutorial = [FullPageGameType.RushRoulette, FullPageGameType.RushAtomRoulette].includes(gameType)

  return (
    <div className={`fullpage fullpage_${gameType}`}>
      <div
        className={`fullpage__banner fullpage__banner_${gameType} ${isOnline ? 'fullpage__banner_online' : ''}`}
        style={{ backgroundImage }}
      >
        {isOnline && <button className="fullpage__button" onClick={onPlay}>{t('playNow')}</button>}
      </div>
      <div className="fullpage__content">
        <div className="fullpage__item fullpage__item_thumb">
          {isTutorial ?
            <Tutorial gameType={gameType} orientation={orientation} isMobile={isMobile} /> :
            <Poster gameType={gameType} dealerPicture={dealerPicture} isOnline={isOnline} />}
        </div>
        <div className="fullpage__item fullpage__item_info">
          <div className="fullpage__title">{t(`${gameType}.title`)}</div>
          <div className="fullpage__limit">
            {selectedLimitString}
            {isLimitEditable && selectedLimitString && <div className="fullpage__edit" onClick={onLimitEdit}>
              <IconEdit />
            </div>}
          </div>
          <div className="fullpage__text" dangerouslySetInnerHTML={{
            __html: t(`${gameType}.description`) ?? ''
          }} />
        </div>
      </div>
    </div>
  )
}
