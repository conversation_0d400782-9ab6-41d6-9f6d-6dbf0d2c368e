@use '../../styles/variables';

$assets-path: './assets/';

.fullpage {
  $this: &;

  position: relative;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  width: 100%;
  height: 100%;
  color: #fff;
  overflow: hidden;
  font-size: 1.16vh;

  &__limit {
    display: flex;
    align-items: center;
    height: 1.4em;
    padding: 0 0.25em;
    font-size: 2em;
    color: variables.$color-highlight;;
    border-bottom-right-radius: .25em;
    background: linear-gradient(to bottom, #000 0%, rgba(0, 0, 0, 0.7) 100%);
  }

  &__edit {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1em;
    height: 1em;
    margin-left: 0.2em;
    margin-bottom: -0.1em;
    cursor: pointer;

    svg {
      width: 75%;
      height: 75%;
      fill: #606060;
      transition: color 0.15s ease-in-out;
    }

    &:hover {
      svg {
        fill: variables.$color-highlight;
      }
    }
  }

  &__banner {
    position: relative;
    aspect-ratio: 21/5;
    width: 100%;
    height: 30vw;
    max-height: 45%;
    margin: 0 auto;
    background-position: center;
    background-size: auto 100%;
    background-repeat: no-repeat;
    background-color: #000;

    &_online {
      cursor: pointer;
    }
  }

  &__button {
    position: absolute;
    bottom: 2em;
    left: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 8em;
    height: 2em;
    border-radius: 1em;
    border: 0.15em solid #fff;
    background-color: #1fa40b;
    transform: translateX(-50%);
    color: #fff;
    font-family: 'Lato-Regular', 'NotoSansCJKsc', Arial, sans-serif;
    font-size: 3em;
    font-weight: 700;
    line-height: 1;
    text-transform: uppercase;
    box-shadow: 0 0.15em 0.2em #000000B8;
    padding: 0 1vw;
    box-sizing: border-box;
    cursor: pointer;
  }

  &__content {
    flex: 1;
    display: flex;
    width: 100%;
    height: calc(100% - 30vw);
    max-width: 140em;
    margin: 0 auto;
    padding: 6em 6em calc(43px + 6em);
    overflow: hidden;
  }

  &__item {
    overflow: hidden;
    flex: 1;

    &_info {
      display: flex;
      flex-direction: column;
      padding-left: 6em;
    }

    &_thumb {
      flex-shrink: 0;
      max-width: 50%;
    }
  }

  &__title {
    font-size: 3.75em;
    text-transform: uppercase;
  }

  &__current-limit {
    position: static;
    color: variables.$color-highlight;
    font-size: 2.2em;
    padding: 0.25em 0;
  }

  &__text {
    flex: 1;
    font-size: 1.7em;
    line-height: 1.2em;
    overflow-y: auto;

    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background: #282a35;
    }

    &::-webkit-scrollbar {
      width: 8px;
      background: #282a35;
    }

    &::-webkit-scrollbar-thumb {
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 3px;
      background-color: #000;
    }

    &::-webkit-scrollbar:horizontal {
      display: none;
    }
  }

  .desktop & {
    @media (orientation: portrait) {
      @media (max-width: 1280px) {
        overflow-y: auto;

        &__banner {
          height: auto;
          aspect-ratio: 9 / 2.7;
        }

        &::-webkit-scrollbar-track {
          border-radius: 10px;
          background: #282a35;
        }

        &::-webkit-scrollbar {
          width: 8px;
          background: #282a35;
        }

        &::-webkit-scrollbar-thumb {
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 3px;
          background-color: #000;
        }

        &::-webkit-scrollbar:horizontal {
          display: none;
        }

        &__content {
          font-size: 1.2vw;
          flex-shrink: 0;
          flex-direction: column;
          padding: 2em 0 calc(1em + 32px);
          overflow: unset;
        }

        &_rush-roulette,
        &_rush-atom-roulette {
          #{$this}__content {
            grid-template-rows: auto;
          }
        }

        &__item {
          width: 100%;
          flex-grow: 0;
          overflow: unset;

          &_thumb {
            order: 2;
            padding: 0 2em 0;
            max-width: 700px;
            margin: 0 auto;
          }

          &_info {
            padding: 0 2em 2em;
          }
        }

        &__text {
          overflow: unset;
        }

        &__button {
          font-size: 3.5vw;
        }
      }

      @media (max-width: 767px) {
        &__content {
          font-size: 2vw;
        }
      }

      @media (max-width: 499px) {
        &__content {
          font-size: 2.5vw;
        }
      }

    }
  }

  .mobile & {
    &__banner {
      height: auto;
    }

    @media (orientation: portrait) {
      overflow-y: auto;

      &::-webkit-scrollbar-track {
        border-radius: 10px;
        background: #282a35;
      }

      &::-webkit-scrollbar {
        width: 8px;
        background: #282a35;
      }

      &::-webkit-scrollbar-thumb {
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 3px;
        background-color: #000;
      }

      &::-webkit-scrollbar:horizontal {
        display: none;
      }

      &__banner {
        aspect-ratio: 9/4;
      }

      &__content {
        flex-shrink: 0;
        flex-direction: column;
        padding: 2em 0 calc(1em + 32px);
        overflow: unset;
      }

      &_rush-roulette,
      &_rush-atom-roulette {
        #{$this}__content {
          grid-template-rows: auto;
        }
      }

      &__item {
        width: 100%;
        overflow: unset;

        &_thumb {
          order: 2;
          padding: 0 2em 0;
          max-width: 100%;
        }

        &_info {
          padding: 0 2em 2em;
        }
      }

      &__text {
        overflow: unset;
      }

      &__button {
        font-size: 3.5vw;
      }
    }

    @media (orientation: landscape) {
      &__content {
        max-width: 100%;
        padding: 4em 4em calc(32px + 2em);
      }

      &__item {
        flex: unset;
        width: auto;

        &_info {
          padding-left: 4em;
        }

        &_thumb {
          height: 100%;
          flex-shrink: 0;
        }
      }

      &__limit {
        font-size: 3.5em;
      }

      &__title {
        font-size: 5em;
      }

      &__text {
        font-size: 2.65em;
      }

      &__button {
        font-size: 3em;
      }
    }
  }
}
