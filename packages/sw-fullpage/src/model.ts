export interface SwFullPageProps {
  dealerPicture?: string
  language?: string
  gameType: FullPageGameType
  isOnline: boolean
  isMobile: boolean
  isPortrait: boolean
  onPlay: () => void
  onLimitEdit: () => void
  selectedLimitString?: string
  isLimitEditable: boolean
}

export enum FullPageGameType {
  BlackjackMax = 'blackjack-max',
  JokersWheel = 'jokers-wheel',
  RushRoulette = 'rush-roulette',
  RushAtomRoulette = 'rush-atom-roulette',
  RushRouletteShow = 'rush-roulette-show',
  RushAtomRouletteShow = 'rush-atom-roulette-show',
}

export enum Orientations {
  Desktop = 'desktop',
  Landscape = 'landscape',
  Portrait = 'portrait'
}

export const BASE_URL = '/widgets/widget-fullpage'
