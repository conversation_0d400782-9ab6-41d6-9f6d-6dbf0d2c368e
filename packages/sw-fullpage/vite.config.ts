import react from '@vitejs/plugin-react';
import { resolve } from 'node:path';
import { defineConfig } from 'vite';
import dts from 'vite-plugin-dts';
import bundleScss from 'rollup-plugin-bundle-scss';

export default defineConfig({
  resolve:{
    alias:{
      '@' : resolve(__dirname, './src')
    },
  },
  plugins: [
    react(),
    dts({
      insertTypesEntry: true,
    }),
    bundleScss({
      exclusive: false
    }),
  ],
  build: {
    sourcemap: true,
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'sw-fullpage',
      formats: ['es', 'umd'],
      fileName: (format) => `sw-fullpage.${format}.js`,
    },
    rollupOptions: {
      external: ['react', 'react-dom'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
        },
      },
    }
  }
});
